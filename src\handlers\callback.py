"""
Callback query handlers for inline keyboards
"""

import logging
from telegram import Update
from telegram.ext import ContextTypes

from src.utils.keyboards import Keyboards
from src.utils.helpers import MessageFormatter
from src.utils.logger import log_user_action
from config import Config

logger = logging.getLogger(__name__)

class CallbackHandler:
    """Handler for callback queries from inline keyboards"""
    
    def __init__(self, bot):
        self.bot = bot
    
    async def handle_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle callback queries"""
        try:
            query = update.callback_query
            user_id = query.from_user.id
            data = query.data
            
            await query.answer()
            
            # Check user permissions
            permissions = await self.bot.check_user_permissions(user_id)
            if not permissions['allowed']:
                await query.edit_message_text(permissions['reason'])
                return
            
            # Route callback based on data
            if data == "verify_channels":
                await self._handle_channel_verification(query, context)
            elif data.startswith("balance_"):
                await self._handle_balance_callbacks(query, context, data)
            elif data.startswith("referral_"):
                await self._handle_referral_callbacks(query, context, data)
            elif data.startswith("withdraw_"):
                await self._handle_withdrawal_callbacks(query, context, data)
            elif data.startswith("product_"):
                await self._handle_product_callbacks(query, context, data)
            elif data.startswith("admin_"):
                await self._handle_admin_callbacks(query, context, data)
            elif data.startswith("settings_"):
                await self._handle_settings_callbacks(query, context, data)
            else:
                await query.edit_message_text("❌ Unknown action.")
            
        except Exception as e:
            logger.error(f"Error handling callback: {e}")
            try:
                await query.answer("❌ An error occurred.")
            except:
                pass
    
    async def _handle_channel_verification(self, query, context):
        """Handle channel verification"""
        try:
            user_id = query.from_user.id
            
            # Verify user subscriptions
            verification_result = await self.bot.channel_service.verify_user_subscriptions(user_id)
            
            if verification_result['all_joined']:
                await query.edit_message_text(
                    "✅ **Verification Successful!**\n\nWelcome to the bot! You can now use all features.",
                    parse_mode='Markdown'
                )
                
                # Send main menu
                await context.bot.send_message(
                    chat_id=user_id,
                    text="Choose an option:",
                    reply_markup=Keyboards.main_menu()
                )
                
                log_user_action(user_id, "CHANNELS_VERIFIED")
            else:
                missing_channels = verification_result['missing_channels']
                await query.edit_message_text(
                    f"❌ Please join all required channels first.\n\nMissing: {len(missing_channels)} channels",
                    reply_markup=Keyboards.channel_join_keyboard(missing_channels)
                )
            
        except Exception as e:
            logger.error(f"Error in channel verification: {e}")
    
    async def _handle_balance_callbacks(self, query, context, data):
        """Handle balance-related callbacks"""
        try:
            user_id = query.from_user.id
            action = data.replace("balance_", "")
            
            if action == "history":
                await self._show_transaction_history(query, context)
            elif action == "refresh":
                await self._refresh_balance(query, context)
            else:
                await query.edit_message_text("❌ Unknown balance action.")
            
        except Exception as e:
            logger.error(f"Error in balance callback: {e}")
    
    async def _handle_referral_callbacks(self, query, context, data):
        """Handle referral-related callbacks"""
        try:
            user_id = query.from_user.id
            action = data.replace("referral_", "")
            
            if action == "copy_link":
                await self._copy_referral_link(query, context)
            elif action == "my_referrals":
                await self._show_my_referrals(query, context)
            elif action == "leaderboard":
                await self._show_referral_leaderboard(query, context)
            else:
                await query.edit_message_text("❌ Unknown referral action.")
            
        except Exception as e:
            logger.error(f"Error in referral callback: {e}")
    
    async def _handle_withdrawal_callbacks(self, query, context, data):
        """Handle withdrawal-related callbacks"""
        try:
            user_id = query.from_user.id
            action = data.replace("withdraw_", "")
            
            if action == "products":
                await self._show_withdrawal_products(query, context)
            elif action == "cash":
                await self._show_cash_withdrawal(query, context)
            elif action == "history":
                await self._show_withdrawal_history(query, context)
            elif action == "menu":
                await self._show_withdrawal_menu(query, context)
            else:
                await query.edit_message_text("❌ Unknown withdrawal action.")
            
        except Exception as e:
            logger.error(f"Error in withdrawal callback: {e}")
    
    async def _handle_product_callbacks(self, query, context, data):
        """Handle product-related callbacks"""
        try:
            user_id = query.from_user.id
            
            if data.startswith("product_"):
                product_id = data.replace("product_", "")
                await self._show_product_details(query, context, product_id)
            elif data.startswith("purchase_"):
                product_id = data.replace("purchase_", "")
                await self._show_purchase_confirmation(query, context, product_id)
            elif data.startswith("confirm_purchase_"):
                product_id = data.replace("confirm_purchase_", "")
                await self._process_product_purchase(query, context, product_id)
            
        except Exception as e:
            logger.error(f"Error in product callback: {e}")
    
    async def _show_transaction_history(self, query, context):
        """Show user transaction history"""
        try:
            user_id = query.from_user.id
            
            transactions = await self.bot.transaction_service.get_user_transactions(user_id, 10)
            
            if not transactions:
                await query.edit_message_text("📋 No transactions found.")
                return
            
            history_text = "📋 **Recent Transactions**\n\n"
            
            for transaction in transactions:
                date = transaction.created_at.strftime('%d/%m %H:%M')
                amount = f"+₹{transaction.amount:.2f}" if transaction.amount > 0 else f"₹{transaction.amount:.2f}"
                status_emoji = "✅" if transaction.status.value == "completed" else "⏳"
                
                history_text += f"{status_emoji} {amount} - {transaction.get_type_display()}\n"
                history_text += f"   📅 {date}\n\n"
            
            await query.edit_message_text(
                history_text,
                parse_mode='Markdown',
                reply_markup=Keyboards.balance_menu()
            )
            
        except Exception as e:
            logger.error(f"Error showing transaction history: {e}")
    
    async def _refresh_balance(self, query, context):
        """Refresh and show current balance"""
        try:
            user_id = query.from_user.id
            
            user = await self.bot.user_service.get_user(user_id)
            if not user:
                await query.edit_message_text("❌ User not found.")
                return
            
            balance_text = MessageFormatter.format_balance(user.balance, user.total_earned)
            
            await query.edit_message_text(
                balance_text,
                parse_mode='Markdown',
                reply_markup=Keyboards.balance_menu()
            )
            
        except Exception as e:
            logger.error(f"Error refreshing balance: {e}")
    
    async def _copy_referral_link(self, query, context):
        """Show referral link for copying"""
        try:
            user_id = query.from_user.id
            
            user = await self.bot.user_service.get_user(user_id)
            if not user:
                await query.edit_message_text("❌ User not found.")
                return
            
            referral_link = f"https://t.me/{Config.BOT_USERNAME}?start={user.referral_code}"
            
            copy_text = f"""
📋 **Your Referral Link**

`{referral_link}`

**How to share:**
1. Copy the link above
2. Share with friends on social media
3. Earn ₹{Config.REFERRAL_REWARD} for each successful referral!

**Tips:**
• Share in groups and channels
• Add a personal message
• Explain the benefits
            """
            
            await query.edit_message_text(
                copy_text,
                parse_mode='Markdown',
                reply_markup=Keyboards.referral_menu()
            )
            
        except Exception as e:
            logger.error(f"Error copying referral link: {e}")
    
    async def _show_withdrawal_products(self, query, context):
        """Show available products for withdrawal"""
        try:
            user_id = query.from_user.id
            
            user = await self.bot.user_service.get_user(user_id)
            if not user or user.balance < Config.MINIMUM_WITHDRAWAL:
                await query.edit_message_text(
                    f"❌ Minimum balance of ₹{Config.MINIMUM_WITHDRAWAL} required for withdrawal."
                )
                return
            
            products = await self.bot.product_service.get_available_products()
            
            if not products:
                await query.edit_message_text("❌ No products available at the moment.")
                return
            
            # Filter products user can afford
            affordable_products = [p for p in products if p.price <= user.balance]
            
            if not affordable_products:
                await query.edit_message_text(
                    f"❌ No products available for your balance of ₹{user.balance:.2f}"
                )
                return
            
            products_text = f"🛍️ **Available Products**\n\nYour Balance: ₹{user.balance:.2f}\n\n"
            
            await query.edit_message_text(
                products_text,
                parse_mode='Markdown',
                reply_markup=Keyboards.products_menu(affordable_products)
            )
            
        except Exception as e:
            logger.error(f"Error showing withdrawal products: {e}")
    
    async def _show_product_details(self, query, context, product_id):
        """Show product details"""
        try:
            product = await self.bot.product_service.get_product(product_id)
            if not product:
                await query.edit_message_text("❌ Product not found.")
                return
            
            details_text = f"""
🛍️ **{product.name}**

**Price:** ₹{product.price:.2f}
**Category:** {product.category}

**Description:**
{product.description}

**Stock:** {product.get_stock_display()}
**Status:** {product.get_status_display()}

{product.terms_conditions or ''}
            """
            
            await query.edit_message_text(
                details_text,
                parse_mode='Markdown',
                reply_markup=Keyboards.product_details(product_id)
            )
            
        except Exception as e:
            logger.error(f"Error showing product details: {e}")
    
    async def _show_purchase_confirmation(self, query, context, product_id):
        """Show purchase confirmation"""
        try:
            user_id = query.from_user.id
            
            user = await self.bot.user_service.get_user(user_id)
            product = await self.bot.product_service.get_product(product_id)
            
            if not user or not product:
                await query.edit_message_text("❌ User or product not found.")
                return
            
            if user.balance < product.price:
                await query.edit_message_text("❌ Insufficient balance.")
                return
            
            confirm_text = f"""
✅ **Confirm Purchase**

**Product:** {product.name}
**Price:** ₹{product.price:.2f}
**Your Balance:** ₹{user.balance:.2f}
**After Purchase:** ₹{user.balance - product.price:.2f}

**Delivery:** Digital product will be delivered within 24-48 hours after admin approval.

Are you sure you want to purchase this product?
            """
            
            await query.edit_message_text(
                confirm_text,
                parse_mode='Markdown',
                reply_markup=Keyboards.confirm_purchase(product_id)
            )
            
        except Exception as e:
            logger.error(f"Error showing purchase confirmation: {e}")
    
    async def _process_product_purchase(self, query, context, product_id):
        """Process product purchase"""
        try:
            user_id = query.from_user.id
            
            # Create withdrawal request
            withdrawal = await self.bot.withdrawal_service.create_withdrawal_request(
                user_id=user_id,
                amount=500,  # Assuming all products are ₹500
                withdrawal_type="product",
                product_id=product_id
            )
            
            if withdrawal:
                await query.edit_message_text(
                    "✅ **Purchase Request Submitted!**\n\n"
                    "Your withdrawal request has been created and is pending admin approval.\n"
                    "You will be notified once it's processed.\n\n"
                    "Expected delivery: 24-48 hours",
                    parse_mode='Markdown'
                )
                
                log_user_action(user_id, "PRODUCT_PURCHASE_REQUESTED", f"Product: {product_id}")
            else:
                await query.edit_message_text("❌ Failed to create purchase request.")
            
        except Exception as e:
            logger.error(f"Error processing product purchase: {e}")
    
    async def _handle_admin_callbacks(self, query, context, data):
        """Handle admin-related callbacks"""
        # This would be implemented with full admin functionality
        await query.edit_message_text("🚧 Admin functionality coming soon...")
    
    async def _handle_settings_callbacks(self, query, context, data):
        """Handle settings-related callbacks"""
        # This would be implemented with settings functionality
        await query.edit_message_text("🚧 Settings functionality coming soon...")
