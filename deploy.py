#!/usr/bin/env python3
"""
Deployment script for the Telegram Referral Bot
"""

import os
import sys
import subprocess
import asyncio
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        sys.exit(1)
    print("✅ Python version check passed")

def check_environment_file():
    """Check if .env file exists"""
    if not Path(".env").exists():
        print("❌ .env file not found")
        print("📝 Please copy .env.example to .env and configure it")
        sys.exit(1)
    print("✅ Environment file found")

def install_dependencies():
    """Install Python dependencies"""
    print("📦 Installing dependencies...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True)
        print("✅ Dependencies installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        sys.exit(1)

def create_directories():
    """Create necessary directories"""
    directories = ["logs", "data", "temp"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")

def validate_configuration():
    """Validate bot configuration"""
    try:
        from config import Config
        Config.validate_config()
        print("✅ Configuration validation passed")
    except Exception as e:
        print(f"❌ Configuration validation failed: {e}")
        sys.exit(1)

async def test_database_connection():
    """Test database connection"""
    try:
        from src.database import Database
        
        db = Database()
        connected = await db.connect()
        
        if connected:
            print("✅ Database connection successful")
            await db.disconnect()
        else:
            print("❌ Database connection failed")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        sys.exit(1)

async def test_bot_token():
    """Test bot token validity"""
    try:
        from telegram import Bot
        from config import Config
        
        bot = Bot(token=Config.BOT_TOKEN)
        bot_info = await bot.get_me()
        
        print(f"✅ Bot token valid - @{bot_info.username}")
        
    except Exception as e:
        print(f"❌ Bot token validation failed: {e}")
        sys.exit(1)

def setup_systemd_service():
    """Setup systemd service for production deployment"""
    service_content = f"""[Unit]
Description=Telegram Referral Bot
After=network.target

[Service]
Type=simple
User={os.getenv('USER', 'ubuntu')}
WorkingDirectory={os.getcwd()}
Environment=PATH={os.getcwd()}/venv/bin
ExecStart={sys.executable} main.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
    
    service_path = Path("/etc/systemd/system/referral-bot.service")
    
    try:
        with open(service_path, 'w') as f:
            f.write(service_content)
        
        subprocess.run(["sudo", "systemctl", "daemon-reload"], check=True)
        subprocess.run(["sudo", "systemctl", "enable", "referral-bot"], check=True)
        
        print("✅ Systemd service created and enabled")
        print("🚀 Use 'sudo systemctl start referral-bot' to start the bot")
        
    except Exception as e:
        print(f"⚠️ Failed to setup systemd service: {e}")
        print("💡 You can run the bot manually with 'python main.py'")

def show_polling_info():
    """Show information about long polling mode"""
    polling_info = """
🔄 **Long Polling Mode Information**

✅ **Advantages:**
• No domain or SSL certificates required
• Simple setup and deployment
• Works behind NAT/firewall
• Perfect for development and testing
• No server configuration needed

📋 **How it works:**
• Bot continuously polls Telegram servers for updates
• Uses getUpdates API with long polling
• Automatically handles connection management
• Suitable for most use cases

🚀 **Deployment:**
• Just run: python main.py
• Bot will start automatically
• No additional server setup required
"""

    print(polling_info)

async def run_deployment():
    """Run the complete deployment process"""
    print("🚀 Starting Telegram Referral Bot Deployment")
    print("=" * 50)
    
    # Pre-deployment checks
    check_python_version()
    check_environment_file()
    
    # Setup
    install_dependencies()
    create_directories()
    validate_configuration()
    
    # Test connections
    await test_database_connection()
    await test_bot_token()
    
    print("\n" + "=" * 50)
    print("✅ Deployment completed successfully!")
    print("\n📋 Next steps:")
    print("1. Review your .env configuration")
    print("2. Start the bot with: python main.py")
    print("3. Bot will run in long polling mode (no domain/SSL needed)")
    print("4. For production, consider setting up systemd service")

    # Optional production setup
    setup_choice = input("\n🤔 Setup systemd service for production? (y/N): ")
    if setup_choice.lower() == 'y':
        setup_systemd_service()

    polling_info_choice = input("🤔 Show long polling mode information? (y/N): ")
    if polling_info_choice.lower() == 'y':
        show_polling_info()

def main():
    """Main deployment function"""
    try:
        asyncio.run(run_deployment())
    except KeyboardInterrupt:
        print("\n❌ Deployment cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Deployment failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
