2025-07-01 05:11:47 - src - INFO - main:29 - Starting Telegram Referral Earning Bot...
2025-07-01 05:11:47 - src - INFO - main:30 - Mode: Long Polling (no webhook/domain required)
2025-07-01 05:11:52 - src.database - ERROR - _initialize_collections:100 - Error initializing collections: Index build failed: fd679877-cef7-4fcf-bec7-5d0cda3b7ff1: Collection referral_bot.users ( ee2886e1-84e7-4639-b15f-9501d14afb5f ) :: caused by :: E11000 duplicate key error collection: referral_bot.users index: referral_code_1 dup key: { referral_code: null }, full error: {'ok': 0.0, 'errmsg': 'Index build failed: fd679877-cef7-4fcf-bec7-5d0cda3b7ff1: Collection referral_bot.users ( ee2886e1-84e7-4639-b15f-9501d14afb5f ) :: caused by :: E11000 duplicate key error collection: referral_bot.users index: referral_code_1 dup key: { referral_code: null }', 'code': 11000, 'codeName': 'DuplicateKey', 'keyPattern': {'referral_code': 1}, 'keyValue': {'referral_code': None}, '$clusterTime': {'clusterTime': Timestamp(1751326912, 28), 'signature': {'hash': b'\x89\x80\xb6A2(\x10\xde#\x90i\xdab\xd5\xd1?\xa9\x86\xb8\xb7', 'keyId': 7471222609279975432}}, 'operationTime': Timestamp(1751326912, 28)}
2025-07-01 05:11:52 - src.database - INFO - connect:35 - Successfully connected to MongoDB Atlas
2025-07-01 05:11:52 - src - INFO - main:35 - Database connected successfully
2025-07-01 05:11:52 - src.bot - INFO - __init__:49 - ReferralBot initialized
2025-07-01 05:11:52 - src.bot - INFO - __init__:49 - ReferralBot initialized
2025-07-01 05:11:53 - src.bot - INFO - _add_handlers:101 - All handlers added successfully
2025-07-01 05:11:53 - src.bot - INFO - _add_handlers:101 - All handlers added successfully
2025-07-01 05:11:53 - src.services.product_service - ERROR - initialize_default_products:400 - Failed to initialize default products: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:11:53 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:11:53 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:11:53 - src.bot - INFO - _start_polling:139 - Starting bot in long polling mode...
2025-07-01 05:11:53 - src.bot - INFO - _start_polling:139 - Starting bot in long polling mode...
2025-07-01 05:11:57 - src.bot - INFO - _start_polling:154 - Bot is running in long polling mode. Press Ctrl+C to stop.
2025-07-01 05:11:57 - src.bot - INFO - _start_polling:154 - Bot is running in long polling mode. Press Ctrl+C to stop.
2025-07-01 05:11:57 - src.bot - INFO - _start_polling:155 - No webhook, domain, or SSL configuration required.
2025-07-01 05:11:57 - src.bot - INFO - _start_polling:155 - No webhook, domain, or SSL configuration required.
2025-07-01 05:11:57 - src.bot - ERROR - start:67 - Failed to start bot: 'Updater' object has no attribute 'idle'
2025-07-01 05:11:57 - src.bot - ERROR - start:67 - Failed to start bot: 'Updater' object has no attribute 'idle'
2025-07-01 05:12:43 - src - INFO - main:29 - Starting Telegram Referral Earning Bot...
2025-07-01 05:12:43 - src - INFO - main:30 - Mode: Long Polling (no webhook/domain required)
2025-07-01 05:12:48 - src.database - ERROR - _initialize_collections:100 - Error initializing collections: Index build failed: e36f9146-93e7-4900-8434-2c3182817196: Collection referral_bot.users ( ee2886e1-84e7-4639-b15f-9501d14afb5f ) :: caused by :: E11000 duplicate key error collection: referral_bot.users index: referral_code_1 dup key: { referral_code: null }, full error: {'ok': 0.0, 'errmsg': 'Index build failed: e36f9146-93e7-4900-8434-2c3182817196: Collection referral_bot.users ( ee2886e1-84e7-4639-b15f-9501d14afb5f ) :: caused by :: E11000 duplicate key error collection: referral_bot.users index: referral_code_1 dup key: { referral_code: null }', 'code': 11000, 'codeName': 'DuplicateKey', 'keyPattern': {'referral_code': 1}, 'keyValue': {'referral_code': None}, '$clusterTime': {'clusterTime': Timestamp(1751326968, 15), 'signature': {'hash': b'\xfcrZ\xc0\x8e\x7f\xedu\xe5\xd6hl\xb2\x19D\x14\xa99\x17\xed', 'keyId': 7471222609279975432}}, 'operationTime': Timestamp(1751326968, 15)}
2025-07-01 05:12:48 - src.database - INFO - connect:35 - Successfully connected to MongoDB Atlas
2025-07-01 05:12:48 - src - INFO - main:35 - Database connected successfully
2025-07-01 05:12:48 - src.bot - INFO - __init__:49 - ReferralBot initialized
2025-07-01 05:12:48 - src.bot - INFO - __init__:49 - ReferralBot initialized
2025-07-01 05:12:49 - src.bot - INFO - _add_handlers:101 - All handlers added successfully
2025-07-01 05:12:49 - src.bot - INFO - _add_handlers:101 - All handlers added successfully
2025-07-01 05:12:49 - src.services.product_service - ERROR - initialize_default_products:400 - Failed to initialize default products: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:12:49 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:12:49 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:12:49 - src.bot - INFO - _start_polling:139 - Starting bot in long polling mode...
2025-07-01 05:12:49 - src.bot - INFO - _start_polling:139 - Starting bot in long polling mode...
2025-07-01 05:12:50 - src.bot - INFO - _start_polling:154 - Bot is running in long polling mode. Press Ctrl+C to stop.
2025-07-01 05:12:50 - src.bot - INFO - _start_polling:154 - Bot is running in long polling mode. Press Ctrl+C to stop.
2025-07-01 05:12:50 - src.bot - INFO - _start_polling:155 - No webhook, domain, or SSL configuration required.
2025-07-01 05:12:50 - src.bot - INFO - _start_polling:155 - No webhook, domain, or SSL configuration required.
2025-07-01 05:12:50 - src.bot - ERROR - start:67 - Failed to start bot: 'Updater' object has no attribute 'idle'
2025-07-01 05:12:50 - src.bot - ERROR - start:67 - Failed to start bot: 'Updater' object has no attribute 'idle'
2025-07-01 05:14:34 - src - INFO - main:29 - Starting Telegram Referral Earning Bot...
2025-07-01 05:14:34 - src - INFO - main:30 - Mode: Long Polling (no webhook/domain required)
2025-07-01 05:14:42 - src.database - INFO - _initialize_collections:97 - Database collections and indexes initialized
2025-07-01 05:14:42 - src.database - INFO - connect:35 - Successfully connected to MongoDB Atlas
2025-07-01 05:14:42 - src - INFO - main:35 - Database connected successfully
2025-07-01 05:14:42 - src.bot - INFO - __init__:49 - ReferralBot initialized
2025-07-01 05:14:42 - src.bot - INFO - __init__:49 - ReferralBot initialized
2025-07-01 05:14:42 - src.bot - INFO - _add_handlers:101 - All handlers added successfully
2025-07-01 05:14:42 - src.bot - INFO - _add_handlers:101 - All handlers added successfully
2025-07-01 05:14:42 - src.services.product_service - ERROR - initialize_default_products:404 - Failed to initialize default products: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:14:42 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:14:42 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:14:42 - src.bot - INFO - _start_polling:139 - Starting bot in long polling mode...
2025-07-01 05:14:42 - src.bot - INFO - _start_polling:139 - Starting bot in long polling mode...
2025-07-01 05:14:42 - src.bot - INFO - _start_polling:140 - Bot is running in long polling mode. Press Ctrl+C to stop.
2025-07-01 05:14:42 - src.bot - INFO - _start_polling:140 - Bot is running in long polling mode. Press Ctrl+C to stop.
2025-07-01 05:14:42 - src.bot - INFO - _start_polling:141 - No webhook, domain, or SSL configuration required.
2025-07-01 05:14:42 - src.bot - INFO - _start_polling:141 - No webhook, domain, or SSL configuration required.
2025-07-01 05:14:42 - src.bot - ERROR - start:67 - Failed to start bot: Cannot close a running event loop
2025-07-01 05:14:42 - src.bot - ERROR - start:67 - Failed to start bot: Cannot close a running event loop
2025-07-01 05:24:43 - __main__ - INFO - initialize:42 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:24:43 - __main__ - INFO - initialize:43 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:24:43 - __main__ - INFO - initialize:47 - ✅ Configuration validated
2025-07-01 05:24:51 - __main__ - INFO - initialize:52 - ✅ Database connected successfully
2025-07-01 05:24:51 - __main__ - INFO - initialize:63 - ✅ Services initialized
2025-07-01 05:24:51 - __main__ - INFO - initialize:68 - ✅ Default products initialized
2025-07-01 05:24:52 - __main__ - INFO - initialize:77 - ✅ All handlers added successfully
2025-07-01 05:24:52 - __main__ - INFO - run:1271 - 🚀 Starting bot in long polling mode...
2025-07-01 05:24:52 - __main__ - INFO - run:1272 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-01 05:24:52 - __main__ - INFO - run:1273 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-01 05:24:52 - __main__ - INFO - run:1274 - 🤖 Bot username: @pro_gifts_bot
2025-07-01 05:24:52 - __main__ - ERROR - run:1290 - ❌ Bot error: Cannot close a running event loop
2025-07-01 05:27:17 - __main__ - INFO - main:905 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:27:17 - __main__ - INFO - main:906 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:27:17 - __main__ - INFO - main:910 - ✅ Configuration validated
2025-07-01 05:27:18 - __main__ - INFO - main:927 - ✅ All handlers added successfully
2025-07-01 05:27:18 - __main__ - INFO - initialize_async_components:42 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:27:18 - __main__ - INFO - initialize_async_components:43 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:27:18 - __main__ - INFO - initialize_async_components:47 - ✅ Configuration validated
2025-07-01 05:27:25 - __main__ - INFO - initialize_async_components:52 - ✅ Database connected successfully
2025-07-01 05:27:25 - __main__ - INFO - initialize_async_components:63 - ✅ Services initialized
2025-07-01 05:27:25 - __main__ - INFO - initialize_async_components:68 - ✅ Default products initialized
2025-07-01 05:27:25 - __main__ - INFO - init_and_run:932 - 🚀 Starting bot in long polling mode...
2025-07-01 05:27:25 - __main__ - INFO - init_and_run:933 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-01 05:27:25 - __main__ - INFO - init_and_run:934 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-01 05:27:25 - __main__ - INFO - init_and_run:935 - 🤖 Bot username: @pro_gifts_bot
2025-07-01 05:34:07 - __main__ - INFO - main:1203 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:34:07 - __main__ - INFO - main:1204 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:34:07 - __main__ - INFO - main:1208 - ✅ Configuration validated
2025-07-01 05:34:08 - __main__ - INFO - main:1225 - ✅ All handlers added successfully
2025-07-01 05:34:08 - __main__ - INFO - initialize_async_components:42 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:34:08 - __main__ - INFO - initialize_async_components:43 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:34:08 - __main__ - INFO - initialize_async_components:47 - ✅ Configuration validated
2025-07-01 05:34:16 - __main__ - INFO - initialize_async_components:52 - ✅ Database connected successfully
2025-07-01 05:34:16 - __main__ - INFO - initialize_async_components:63 - ✅ Services initialized
2025-07-01 05:34:16 - __main__ - INFO - initialize_async_components:68 - ✅ Default products initialized
2025-07-01 05:34:16 - __main__ - INFO - init_and_run:1230 - 🚀 Starting bot in long polling mode...
2025-07-01 05:34:16 - __main__ - INFO - init_and_run:1231 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-01 05:34:16 - __main__ - INFO - init_and_run:1232 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-01 05:34:16 - __main__ - INFO - init_and_run:1233 - 🤖 Bot username: @pro_gifts_bot
2025-07-01 05:44:52 - __main__ - INFO - main:1112 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:44:52 - __main__ - INFO - main:1113 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:44:52 - __main__ - INFO - main:1117 - ✅ Configuration validated
2025-07-01 05:44:53 - __main__ - INFO - main:1134 - ✅ All handlers added successfully
2025-07-01 05:44:53 - __main__ - INFO - initialize_async_components:42 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:44:53 - __main__ - INFO - initialize_async_components:43 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:44:53 - __main__ - INFO - initialize_async_components:47 - ✅ Configuration validated
2025-07-01 05:45:01 - __main__ - INFO - initialize_async_components:52 - ✅ Database connected successfully
2025-07-01 05:45:01 - __main__ - INFO - initialize_async_components:63 - ✅ Services initialized
2025-07-01 05:45:01 - __main__ - INFO - initialize_async_components:68 - ✅ Default products initialized
2025-07-01 05:45:01 - __main__ - INFO - init_and_run:1139 - 🚀 Starting bot in long polling mode...
2025-07-01 05:45:01 - __main__ - INFO - init_and_run:1140 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-01 05:45:01 - __main__ - INFO - init_and_run:1141 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-01 05:45:01 - __main__ - INFO - init_and_run:1142 - 🤖 Bot username: @pro_gifts_bot
2025-07-01 05:49:57 - __main__ - INFO - main:1112 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:49:57 - __main__ - INFO - main:1113 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:49:57 - __main__ - INFO - main:1117 - ✅ Configuration validated
2025-07-01 05:49:58 - __main__ - INFO - main:1134 - ✅ All handlers added successfully
2025-07-01 05:49:58 - __main__ - INFO - initialize_async_components:42 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:49:58 - __main__ - INFO - initialize_async_components:43 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:49:58 - __main__ - INFO - initialize_async_components:47 - ✅ Configuration validated
2025-07-01 05:50:05 - __main__ - INFO - initialize_async_components:52 - ✅ Database connected successfully
2025-07-01 05:50:05 - __main__ - INFO - initialize_async_components:63 - ✅ Services initialized
2025-07-01 05:50:05 - __main__ - INFO - initialize_async_components:68 - ✅ Default products initialized
2025-07-01 05:50:05 - __main__ - INFO - init_and_run:1139 - 🚀 Starting bot in long polling mode...
2025-07-01 05:50:05 - __main__ - INFO - init_and_run:1140 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-01 05:50:05 - __main__ - INFO - init_and_run:1141 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-01 05:50:05 - __main__ - INFO - init_and_run:1142 - 🤖 Bot username: @pro_gifts_bot
2025-07-01 05:55:18 - __main__ - INFO - main:1112 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:55:18 - __main__ - INFO - main:1113 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:55:18 - __main__ - INFO - main:1117 - ✅ Configuration validated
2025-07-01 05:55:19 - __main__ - INFO - main:1134 - ✅ All handlers added successfully
2025-07-01 05:55:19 - __main__ - INFO - initialize_async_components:42 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:55:19 - __main__ - INFO - initialize_async_components:43 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:55:19 - __main__ - INFO - initialize_async_components:47 - ✅ Configuration validated
2025-07-01 05:55:26 - __main__ - INFO - initialize_async_components:52 - ✅ Database connected successfully
2025-07-01 05:55:26 - __main__ - INFO - initialize_async_components:63 - ✅ Services initialized
2025-07-01 05:55:26 - __main__ - INFO - initialize_async_components:68 - ✅ Default products initialized
2025-07-01 05:55:26 - __main__ - INFO - init_and_run:1139 - 🚀 Starting bot in long polling mode...
2025-07-01 05:55:26 - __main__ - INFO - init_and_run:1140 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-01 05:55:26 - __main__ - INFO - init_and_run:1141 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-01 05:55:26 - __main__ - INFO - init_and_run:1142 - 🤖 Bot username: @pro_gifts_bot
2025-07-01 06:28:56 - __main__ - ERROR - error_handler:1095 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-01 06:28:57 - __main__ - ERROR - error_handler:1095 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-01 06:28:59 - __main__ - ERROR - error_handler:1095 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-01 06:29:01 - __main__ - ERROR - error_handler:1095 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-02 09:27:46 - __main__ - ERROR - error_handler:1095 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-06 23:20:14 - src - INFO - main:29 - Starting Telegram Referral Earning Bot...
2025-07-06 23:20:14 - src - INFO - main:30 - Mode: Long Polling (no webhook/domain required)
2025-07-06 23:20:22 - src.database - INFO - _initialize_collections:97 - Database collections and indexes initialized
2025-07-06 23:20:22 - src.database - INFO - connect:35 - Successfully connected to MongoDB Atlas
2025-07-06 23:20:22 - src - INFO - main:35 - Database connected successfully
2025-07-06 23:20:22 - src.bot - INFO - __init__:49 - ReferralBot initialized
2025-07-06 23:20:22 - src.bot - INFO - __init__:49 - ReferralBot initialized
