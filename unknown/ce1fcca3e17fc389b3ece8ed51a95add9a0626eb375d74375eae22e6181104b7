"""
Product service for managing digital products
"""

import logging
from datetime import datetime, timezone
from typing import Optional, List, Dict, Any

from src.database import Database
from src.models.product import Product
from src.utils.logger import log_admin_action
from config import Config

logger = logging.getLogger(__name__)

class ProductService:
    """Service for product management operations"""
    
    def __init__(self, database: Database):
        self.db = database
    
    async def create_product(self, name: str, price: float, description: str, category: str,
                           admin_id: int, **kwargs) -> Optional[Product]:
        """Create a new product"""
        try:
            # Check if product with same name exists
            existing = await self.db.products.find_one({'name': name})
            if existing is not None:
                return None
            
            # Create product
            product = Product(
                name=name,
                price=price,
                description=description,
                category=category,
                added_by=admin_id,
                **kwargs
            )
            
            # Save to database
            await self.db.products.insert_one(product.to_dict())
            
            log_admin_action(admin_id, "PRODUCT_CREATED", product.product_id, f"Name: {name}, Price: {price}")
            logger.info(f"Created product {product.product_id} by admin {admin_id}")
            
            return product
            
        except Exception as e:
            logger.error(f"Failed to create product {name}: {e}")
            return None
    
    async def get_product(self, product_id: str) -> Optional[Product]:
        """Get product by ID"""
        try:
            product_data = await self.db.products.find_one({'product_id': product_id})
            if product_data:
                return Product.from_dict(product_data)
            return None
        except Exception as e:
            logger.error(f"Failed to get product {product_id}: {e}")
            return None
    
    async def get_product_by_name(self, name: str) -> Optional[Product]:
        """Get product by name"""
        try:
            product_data = await self.db.products.find_one({'name': name})
            if product_data:
                return Product.from_dict(product_data)
            return None
        except Exception as e:
            logger.error(f"Failed to get product by name {name}: {e}")
            return None
    
    async def update_product(self, product: Product) -> bool:
        """Update product in database"""
        try:
            product.updated_at = datetime.now(timezone.utc)
            result = await self.db.products.update_one(
                {'product_id': product.product_id},
                {'$set': product.to_dict()}
            )
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Failed to update product {product.product_id}: {e}")
            return False
    
    async def delete_product(self, product_id: str, admin_id: int) -> bool:
        """Delete a product"""
        try:
            product = await self.get_product(product_id)
            if not product:
                return False
            
            result = await self.db.products.delete_one({'product_id': product_id})
            
            if result.deleted_count > 0:
                log_admin_action(admin_id, "PRODUCT_DELETED", product_id, f"Name: {product.name}")
                logger.info(f"Deleted product {product_id} by admin {admin_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to delete product {product_id}: {e}")
            return False
    
    async def get_all_products(self, include_inactive: bool = False) -> List[Product]:
        """Get all products"""
        try:
            query = {} if include_inactive else {'is_active': True}
            cursor = self.db.products.find(query).sort('created_at', -1)
            products = []
            
            async for product_data in cursor:
                products.append(Product.from_dict(product_data))
            
            return products
            
        except Exception as e:
            logger.error(f"Failed to get all products: {e}")
            return []
    
    async def get_products_by_category(self, category: str, include_inactive: bool = False) -> List[Product]:
        """Get products by category"""
        try:
            query = {'category': category}
            if not include_inactive:
                query['is_active'] = True
            
            cursor = self.db.products.find(query).sort('created_at', -1)
            products = []
            
            async for product_data in cursor:
                products.append(Product.from_dict(product_data))
            
            return products
            
        except Exception as e:
            logger.error(f"Failed to get products by category {category}: {e}")
            return []
    
    async def get_available_products(self) -> List[Product]:
        """Get all available products (active and in stock)"""
        try:
            cursor = self.db.products.find({'is_active': True}).sort([('is_featured', -1), ('created_at', -1)])
            products = []
            
            async for product_data in cursor:
                product = Product.from_dict(product_data)
                if product.is_available():
                    products.append(product)
            
            return products
            
        except Exception as e:
            logger.error(f"Failed to get available products: {e}")
            return []
    
    async def get_featured_products(self) -> List[Product]:
        """Get featured products"""
        try:
            cursor = self.db.products.find({
                'is_active': True,
                'is_featured': True
            }).sort('created_at', -1)
            
            products = []
            async for product_data in cursor:
                product = Product.from_dict(product_data)
                if product.is_available():
                    products.append(product)
            
            return products
            
        except Exception as e:
            logger.error(f"Failed to get featured products: {e}")
            return []
    
    async def activate_product(self, product_id: str, admin_id: int) -> bool:
        """Activate a product"""
        try:
            product = await self.get_product(product_id)
            if not product:
                return False
            
            product.activate_product()
            result = await self.update_product(product)
            
            if result:
                log_admin_action(admin_id, "PRODUCT_ACTIVATED", product_id)
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to activate product {product_id}: {e}")
            return False
    
    async def deactivate_product(self, product_id: str, admin_id: int) -> bool:
        """Deactivate a product"""
        try:
            product = await self.get_product(product_id)
            if not product:
                return False
            
            product.deactivate_product()
            result = await self.update_product(product)
            
            if result:
                log_admin_action(admin_id, "PRODUCT_DEACTIVATED", product_id)
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to deactivate product {product_id}: {e}")
            return False
    
    async def set_product_featured(self, product_id: str, featured: bool, admin_id: int) -> bool:
        """Set product as featured or not"""
        try:
            product = await self.get_product(product_id)
            if not product:
                return False
            
            product.set_featured(featured)
            result = await self.update_product(product)
            
            if result:
                action = "PRODUCT_FEATURED" if featured else "PRODUCT_UNFEATURED"
                log_admin_action(admin_id, action, product_id)
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to set featured status for product {product_id}: {e}")
            return False
    
    async def update_product_stock(self, product_id: str, quantity: int, admin_id: int) -> bool:
        """Update product stock quantity"""
        try:
            product = await self.get_product(product_id)
            if not product:
                return False
            
            if quantity >= 0:
                product.stock_quantity = quantity
            else:
                product.stock_quantity = None  # Unlimited
            
            result = await self.update_product(product)
            
            if result:
                log_admin_action(admin_id, "PRODUCT_STOCK_UPDATED", product_id, f"Quantity: {quantity}")
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to update stock for product {product_id}: {e}")
            return False
    
    async def process_product_order(self, product_id: str, user_id: int) -> bool:
        """Process a product order (reduce stock, update statistics)"""
        try:
            product = await self.get_product(product_id)
            if not product or not product.is_available():
                return False
            
            # Reduce stock
            if not product.reduce_stock():
                return False
            
            # Add order
            product.add_order()
            
            result = await self.update_product(product)
            
            if result:
                logger.info(f"Processed order for product {product_id} by user {user_id}")
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to process order for product {product_id}: {e}")
            return False
    
    async def complete_product_order(self, product_id: str) -> bool:
        """Mark product order as completed"""
        try:
            product = await self.get_product(product_id)
            if not product:
                return False
            
            product.complete_order()
            return await self.update_product(product)
            
        except Exception as e:
            logger.error(f"Failed to complete order for product {product_id}: {e}")
            return False
    
    async def cancel_product_order(self, product_id: str) -> bool:
        """Cancel product order (restore stock)"""
        try:
            product = await self.get_product(product_id)
            if not product:
                return False
            
            product.cancel_order()
            product.increase_stock()  # Restore stock
            
            return await self.update_product(product)
            
        except Exception as e:
            logger.error(f"Failed to cancel order for product {product_id}: {e}")
            return False
    
    async def get_product_categories(self) -> List[str]:
        """Get all product categories"""
        try:
            pipeline = [
                {'$group': {'_id': '$category'}},
                {'$sort': {'_id': 1}}
            ]
            
            categories = []
            async for result in self.db.products.aggregate(pipeline):
                categories.append(result['_id'])
            
            return categories
            
        except Exception as e:
            logger.error(f"Failed to get product categories: {e}")
            return []
    
    async def get_product_statistics(self) -> Dict[str, Any]:
        """Get product statistics"""
        try:
            total_products = await self.db.products.count_documents({})
            active_products = await self.db.products.count_documents({'is_active': True})
            featured_products = await self.db.products.count_documents({
                'is_active': True,
                'is_featured': True
            })
            
            # Get total orders and deliveries
            pipeline = [
                {'$group': {
                    '_id': None,
                    'total_orders': {'$sum': '$total_orders'},
                    'successful_deliveries': {'$sum': '$successful_deliveries'},
                    'pending_orders': {'$sum': '$pending_orders'}
                }}
            ]
            
            result = await self.db.products.aggregate(pipeline).to_list(1)
            total_orders = result[0]['total_orders'] if result else 0
            successful_deliveries = result[0]['successful_deliveries'] if result else 0
            pending_orders = result[0]['pending_orders'] if result else 0
            
            # Get most popular products
            popular_products = await self.db.products.find({
                'is_active': True
            }).sort('total_orders', -1).limit(5).to_list(5)
            
            return {
                'total_products': total_products,
                'active_products': active_products,
                'featured_products': featured_products,
                'total_orders': total_orders,
                'successful_deliveries': successful_deliveries,
                'pending_orders': pending_orders,
                'delivery_rate': (successful_deliveries / total_orders * 100) if total_orders > 0 else 0,
                'popular_products': [Product.from_dict(p) for p in popular_products]
            }
            
        except Exception as e:
            logger.error(f"Failed to get product statistics: {e}")
            return {}
    
    async def initialize_default_products(self):
        """Initialize default products if none exist"""
        try:
            if self.db is None or self.db.products is None:
                logger.error("Database products collection is not available")
                return

            existing_count = await self.db.products.count_documents({})
            if existing_count > 0:
                return

            for product_data in Config.DEFAULT_PRODUCTS:
                product = Product(
                    name=product_data['name'],
                    price=product_data['price'],
                    description=product_data['description'],
                    category=product_data['category'],
                    is_featured=True
                )

                await self.db.products.insert_one(product.to_dict())

            logger.info(f"Initialized {len(Config.DEFAULT_PRODUCTS)} default products")

        except Exception as e:
            logger.error(f"Failed to initialize default products: {e}")
