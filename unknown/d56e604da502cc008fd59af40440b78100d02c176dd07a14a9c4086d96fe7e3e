"""
User service for managing user operations
"""

import logging
from datetime import datetime, timezone
from typing import Optional, List, Dict, Any
from pymongo.errors import DuplicateKeyError

from src.database import Database
from src.models.user import User
from src.utils.logger import log_user_action

logger = logging.getLogger(__name__)

class UserService:
    """Service for user management operations"""
    
    def __init__(self, database: Database):
        self.db = database
    
    async def create_user(self, user_data: Dict[str, Any], referral_code: Optional[str] = None) -> Optional[User]:
        """Create a new user"""
        try:
            # Check if user already exists
            existing_user = await self.get_user(user_data['user_id'])
            if existing_user:
                return existing_user
            
            # Create user object
            user = User(
                user_id=user_data['user_id'],
                username=user_data.get('username'),
                first_name=user_data.get('first_name'),
                last_name=user_data.get('last_name'),
                language_code=user_data.get('language_code'),
                is_bot=user_data.get('is_bot', False),
                is_premium=user_data.get('is_premium', False)
            )
            
            # Set referrer if referral code provided
            if referral_code:
                referrer = await self.get_user_by_referral_code(referral_code)
                if referrer and referrer.user_id != user.user_id:
                    user.referred_by = referrer.user_id
            
            # Save to database
            await self.db.users.insert_one(user.to_dict())
            
            log_user_action(user.user_id, "USER_CREATED", f"Referred by: {user.referred_by}")
            logger.info(f"Created new user: {user.user_id}")
            
            return user
            
        except DuplicateKeyError:
            # User already exists, return existing user
            return await self.get_user(user_data['user_id'])
        except Exception as e:
            logger.error(f"Failed to create user {user_data.get('user_id')}: {e}")
            return None
    
    async def get_user(self, user_id: int) -> Optional[User]:
        """Get user by ID"""
        try:
            user_data = await self.db.users.find_one({'user_id': user_id})
            if user_data is not None:
                return User.from_dict(user_data)
            return None
        except Exception as e:
            logger.error(f"Failed to get user {user_id}: {e}")
            return None
    
    async def get_user_by_referral_code(self, referral_code: str) -> Optional[User]:
        """Get user by referral code"""
        try:
            user_data = await self.db.users.find_one({'referral_code': referral_code})
            if user_data is not None:
                return User.from_dict(user_data)
            return None
        except Exception as e:
            logger.error(f"Failed to get user by referral code {referral_code}: {e}")
            return None
    
    async def update_user(self, user: User) -> bool:
        """Update user in database"""
        try:
            user.update_activity()
            result = await self.db.users.update_one(
                {'user_id': user.user_id},
                {'$set': user.to_dict()}
            )
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Failed to update user {user.user_id}: {e}")
            return False
    
    async def update_user_balance(self, user_id: int, amount: float, reason: str = "") -> bool:
        """Update user balance"""
        try:
            user = await self.get_user(user_id)
            if not user:
                return False
            
            if amount > 0:
                user.add_balance(amount, reason)
            else:
                if not user.deduct_balance(abs(amount)):
                    return False
            
            result = await self.update_user(user)
            
            if result:
                log_user_action(user_id, "BALANCE_UPDATED", f"Amount: {amount}, Reason: {reason}")
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to update balance for user {user_id}: {e}")
            return False
    
    async def ban_user(self, user_id: int, reason: str = "", admin_id: Optional[int] = None) -> bool:
        """Ban a user"""
        try:
            user = await self.get_user(user_id)
            if not user:
                return False
            
            user.ban_user(reason)
            result = await self.update_user(user)
            
            if result:
                log_user_action(user_id, "USER_BANNED", f"Reason: {reason}, Admin: {admin_id}")
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to ban user {user_id}: {e}")
            return False
    
    async def unban_user(self, user_id: int, admin_id: Optional[int] = None) -> bool:
        """Unban a user"""
        try:
            user = await self.get_user(user_id)
            if not user:
                return False
            
            user.unban_user()
            result = await self.update_user(user)
            
            if result:
                log_user_action(user_id, "USER_UNBANNED", f"Admin: {admin_id}")
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to unban user {user_id}: {e}")
            return False
    
    async def get_users_count(self) -> int:
        """Get total number of users"""
        try:
            return await self.db.users.count_documents({})
        except Exception as e:
            logger.error(f"Failed to get users count: {e}")
            return 0
    
    async def get_active_users_count(self) -> int:
        """Get number of active users"""
        try:
            return await self.db.users.count_documents({'is_active': True, 'is_banned': False})
        except Exception as e:
            logger.error(f"Failed to get active users count: {e}")
            return 0
    
    async def get_users_paginated(self, page: int = 0, per_page: int = 20) -> List[User]:
        """Get users with pagination"""
        try:
            skip = page * per_page
            cursor = self.db.users.find().sort('created_at', -1).skip(skip).limit(per_page)
            users = []
            
            async for user_data in cursor:
                users.append(User.from_dict(user_data))
            
            return users
            
        except Exception as e:
            logger.error(f"Failed to get paginated users: {e}")
            return []
    
    async def search_users(self, query: str, limit: int = 20) -> List[User]:
        """Search users by username, first name, or user ID"""
        try:
            # Try to search by user ID if query is numeric
            search_filters = []

            if query.isdigit():
                search_filters.append({'user_id': int(query)})

            # Search by username and name
            search_filters.extend([
                {'username': {'$regex': query, '$options': 'i'}},
                {'first_name': {'$regex': query, '$options': 'i'}},
                {'last_name': {'$regex': query, '$options': 'i'}}
            ])

            cursor = self.db.users.find({'$or': search_filters}).limit(limit)
            users = []

            async for user_data in cursor:
                users.append(User.from_dict(user_data))

            return users

        except Exception as e:
            logger.error(f"Failed to search users: {e}")
            return []

    async def cleanup_incomplete_registrations(self, max_age_minutes: int = 30) -> int:
        """Remove users who didn't complete registration within the specified time"""
        try:
            from datetime import datetime, timezone, timedelta

            # Calculate cutoff time
            cutoff_time = datetime.now(timezone.utc) - timedelta(minutes=max_age_minutes)

            # Find incomplete registrations older than cutoff
            query = {
                'registration_completed': {'$ne': True},  # Not completed or field doesn't exist
                'created_at': {'$lt': cutoff_time}
            }

            # Count documents to be deleted
            count = await self.db.users.count_documents(query)

            if count > 0:
                # Delete incomplete registrations
                result = await self.db.users.delete_many(query)
                logger.info(f"Cleaned up {result.deleted_count} incomplete user registrations")
                return result.deleted_count

            return 0

        except Exception as e:
            logger.error(f"Failed to cleanup incomplete registrations: {e}")
            return 0

    async def mark_registration_completed(self, user_id: int) -> bool:
        """Mark user registration as completed"""
        try:
            user = await self.get_user(user_id)
            if user:
                user.complete_registration()
                return await self.update_user(user)
            return False
        except Exception as e:
            logger.error(f"Failed to mark registration completed for user {user_id}: {e}")
            return False
    
    async def get_top_referrers(self, limit: int = 10) -> List[User]:
        """Get top users by referral count"""
        try:
            cursor = self.db.users.find().sort('referral_count', -1).limit(limit)
            users = []
            
            async for user_data in cursor:
                users.append(User.from_dict(user_data))
            
            return users
            
        except Exception as e:
            logger.error(f"Failed to get top referrers: {e}")
            return []
    
    async def get_top_earners(self, limit: int = 10) -> List[User]:
        """Get top users by total earnings"""
        try:
            cursor = self.db.users.find().sort('total_earned', -1).limit(limit)
            users = []
            
            async for user_data in cursor:
                users.append(User.from_dict(user_data))
            
            return users
            
        except Exception as e:
            logger.error(f"Failed to get top earners: {e}")
            return []
    
    async def update_user_channel_status(self, user_id: int, channel_id: str, joined: bool) -> bool:
        """Update user's channel join status"""
        try:
            user = await self.get_user(user_id)
            if not user:
                return False
            
            if joined:
                user.join_channel(channel_id)
            else:
                user.leave_channel(channel_id)
            
            return await self.update_user(user)
            
        except Exception as e:
            logger.error(f"Failed to update channel status for user {user_id}: {e}")
            return False
    
    async def get_user_statistics(self, user_id: int) -> Dict[str, Any]:
        """Get comprehensive user statistics"""
        try:
            user = await self.get_user(user_id)
            if not user:
                return {}
            
            # Get referral count
            referral_count = await self.db.referrals.count_documents({'referrer_id': user_id})
            
            # Get transaction count
            transaction_count = await self.db.transactions.count_documents({'user_id': user_id})
            
            # Get withdrawal count
            withdrawal_count = await self.db.withdrawals.count_documents({'user_id': user_id})
            
            return {
                'user': user.to_dict(),
                'referral_count': referral_count,
                'transaction_count': transaction_count,
                'withdrawal_count': withdrawal_count,
                'days_since_join': (datetime.now(timezone.utc) - user.created_at).days,
                'can_withdraw': user.balance >= 500  # Assuming minimum withdrawal is 500
            }
            
        except Exception as e:
            logger.error(f"Failed to get user statistics for {user_id}: {e}")
            return {}
