"""
Transaction service for managing financial transactions
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Optional, List, Dict, Any

from src.database import Database
from src.models.transaction import Transaction, TransactionType, TransactionStatus
from src.utils.logger import log_transaction

logger = logging.getLogger(__name__)

class TransactionService:
    """Service for transaction management operations"""
    
    def __init__(self, database: Database):
        self.db = database
    
    async def create_transaction(self, user_id: int, amount: float, transaction_type: TransactionType,
                               description: str = "", reference_id: str = "", metadata: Dict = None) -> Optional[Transaction]:
        """Create a new transaction"""
        try:
            transaction = Transaction(
                user_id=user_id,
                amount=amount,
                transaction_type=transaction_type,
                description=description,
                reference_id=reference_id,
                metadata=metadata or {}
            )
            
            # Save to database
            await self.db.transactions.insert_one(transaction.to_dict())
            
            log_transaction(user_id, transaction_type.value, amount, "PENDING", description)
            logger.info(f"Created transaction {transaction.transaction_id} for user {user_id}")
            
            return transaction
            
        except Exception as e:
            logger.error(f"Failed to create transaction for user {user_id}: {e}")
            return None
    
    async def get_transaction(self, transaction_id: str) -> Optional[Transaction]:
        """Get transaction by ID"""
        try:
            transaction_data = await self.db.transactions.find_one({'transaction_id': transaction_id})
            if transaction_data:
                return Transaction.from_dict(transaction_data)
            return None
        except Exception as e:
            logger.error(f"Failed to get transaction {transaction_id}: {e}")
            return None
    
    async def update_transaction(self, transaction: Transaction) -> bool:
        """Update transaction in database"""
        try:
            transaction.updated_at = datetime.now(timezone.utc)
            result = await self.db.transactions.update_one(
                {'transaction_id': transaction.transaction_id},
                {'$set': transaction.to_dict()}
            )
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Failed to update transaction {transaction.transaction_id}: {e}")
            return False
    
    async def complete_transaction(self, transaction_id: str, admin_id: Optional[int] = None, notes: str = "") -> bool:
        """Complete a transaction"""
        try:
            transaction = await self.get_transaction(transaction_id)
            if not transaction:
                return False
            
            transaction.complete_transaction(admin_id, notes)
            result = await self.update_transaction(transaction)
            
            if result:
                log_transaction(
                    transaction.user_id,
                    transaction.transaction_type.value,
                    transaction.amount,
                    "COMPLETED",
                    f"Admin: {admin_id}, Notes: {notes}"
                )
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to complete transaction {transaction_id}: {e}")
            return False
    
    async def fail_transaction(self, transaction_id: str, reason: str = "", admin_id: Optional[int] = None) -> bool:
        """Fail a transaction"""
        try:
            transaction = await self.get_transaction(transaction_id)
            if not transaction:
                return False
            
            transaction.fail_transaction(reason, admin_id)
            result = await self.update_transaction(transaction)
            
            if result:
                log_transaction(
                    transaction.user_id,
                    transaction.transaction_type.value,
                    transaction.amount,
                    "FAILED",
                    f"Reason: {reason}, Admin: {admin_id}"
                )
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to fail transaction {transaction_id}: {e}")
            return False
    
    async def get_user_transactions(self, user_id: int, limit: int = 50, transaction_type: Optional[TransactionType] = None) -> List[Transaction]:
        """Get transactions for a user"""
        try:
            query = {'user_id': user_id}
            if transaction_type:
                query['transaction_type'] = transaction_type.value

            cursor = self.db.transactions.find(query).sort('created_at', -1).limit(limit)
            transactions = []

            async for transaction_data in cursor:
                transactions.append(Transaction.from_dict(transaction_data))

            return transactions

        except Exception as e:
            logger.error(f"Failed to get transactions for user {user_id}: {e}")
            return []

    async def get_user_transactions_since(self, user_id: int, since_date: datetime, transaction_type: Optional[TransactionType] = None) -> List[Transaction]:
        """Get transactions for a user since a specific date"""
        try:
            query = {
                'user_id': user_id,
                'created_at': {'$gte': since_date.isoformat()}
            }
            if transaction_type:
                query['transaction_type'] = transaction_type.value

            cursor = self.db.transactions.find(query).sort('created_at', -1)
            transactions = []

            async for transaction_data in cursor:
                transactions.append(Transaction.from_dict(transaction_data))

            return transactions

        except Exception as e:
            logger.error(f"Failed to get transactions for user {user_id} since {since_date}: {e}")
            return []
    
    async def get_user_balance_from_transactions(self, user_id: int) -> float:
        """Calculate user balance from transactions"""
        try:
            pipeline = [
                {'$match': {
                    'user_id': user_id,
                    'status': TransactionStatus.COMPLETED.value
                }},
                {'$group': {
                    '_id': None,
                    'total': {'$sum': '$amount'}
                }}
            ]
            
            result = await self.db.transactions.aggregate(pipeline).to_list(1)
            
            if result:
                return result[0]['total']
            return 0.0
            
        except Exception as e:
            logger.error(f"Failed to calculate balance for user {user_id}: {e}")
            return 0.0
    
    async def get_transaction_statistics(self, days: int = 30) -> Dict[str, Any]:
        """Get transaction statistics for the last N days"""
        try:
            start_date = datetime.now(timezone.utc) - timedelta(days=days)
            
            # Total transactions
            total_transactions = await self.db.transactions.count_documents({
                'created_at': {'$gte': start_date.isoformat()}
            })
            
            # Completed transactions
            completed_transactions = await self.db.transactions.count_documents({
                'created_at': {'$gte': start_date.isoformat()},
                'status': TransactionStatus.COMPLETED.value
            })
            
            # Total amount
            pipeline = [
                {'$match': {
                    'created_at': {'$gte': start_date.isoformat()},
                    'status': TransactionStatus.COMPLETED.value
                }},
                {'$group': {
                    '_id': None,
                    'total_amount': {'$sum': '$amount'},
                    'avg_amount': {'$avg': '$amount'}
                }}
            ]
            
            result = await self.db.transactions.aggregate(pipeline).to_list(1)
            total_amount = result[0]['total_amount'] if result else 0.0
            avg_amount = result[0]['avg_amount'] if result else 0.0
            
            # Transaction types breakdown
            type_pipeline = [
                {'$match': {
                    'created_at': {'$gte': start_date.isoformat()},
                    'status': TransactionStatus.COMPLETED.value
                }},
                {'$group': {
                    '_id': '$transaction_type',
                    'count': {'$sum': 1},
                    'total_amount': {'$sum': '$amount'}
                }}
            ]
            
            type_breakdown = {}
            async for entry in self.db.transactions.aggregate(type_pipeline):
                type_breakdown[entry['_id']] = {
                    'count': entry['count'],
                    'total_amount': entry['total_amount']
                }
            
            return {
                'period_days': days,
                'total_transactions': total_transactions,
                'completed_transactions': completed_transactions,
                'completion_rate': (completed_transactions / total_transactions * 100) if total_transactions > 0 else 0,
                'total_amount': total_amount,
                'average_amount': avg_amount,
                'type_breakdown': type_breakdown
            }
            
        except Exception as e:
            logger.error(f"Failed to get transaction statistics: {e}")
            return {}
    
    async def get_daily_transaction_data(self, days: int = 30) -> List[Dict[str, Any]]:
        """Get daily transaction data for charts"""
        try:
            start_date = datetime.now(timezone.utc) - timedelta(days=days)
            
            pipeline = [
                {'$match': {
                    'created_at': {'$gte': start_date.isoformat()},
                    'status': TransactionStatus.COMPLETED.value
                }},
                {'$group': {
                    '_id': {
                        '$dateToString': {
                            'format': '%Y-%m-%d',
                            'date': {'$dateFromString': {'dateString': '$created_at'}}
                        }
                    },
                    'count': {'$sum': 1},
                    'total_amount': {'$sum': '$amount'}
                }},
                {'$sort': {'_id': 1}}
            ]
            
            daily_data = []
            async for entry in self.db.transactions.aggregate(pipeline):
                daily_data.append({
                    'date': datetime.strptime(entry['_id'], '%Y-%m-%d'),
                    'count': entry['count'],
                    'amount': entry['total_amount']
                })
            
            return daily_data
            
        except Exception as e:
            logger.error(f"Failed to get daily transaction data: {e}")
            return []
    
    async def get_pending_transactions(self, limit: int = 100) -> List[Transaction]:
        """Get pending transactions"""
        try:
            cursor = self.db.transactions.find({
                'status': TransactionStatus.PENDING.value
            }).sort('created_at', 1).limit(limit)
            
            transactions = []
            async for transaction_data in cursor:
                transactions.append(Transaction.from_dict(transaction_data))
            
            return transactions
            
        except Exception as e:
            logger.error(f"Failed to get pending transactions: {e}")
            return []
    
    async def cleanup_old_transactions(self, days: int = 365) -> int:
        """Clean up old completed transactions (keep for audit)"""
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
            
            # Only delete very old failed/cancelled transactions
            result = await self.db.transactions.delete_many({
                'created_at': {'$lt': cutoff_date.isoformat()},
                'status': {'$in': [TransactionStatus.FAILED.value, TransactionStatus.CANCELLED.value]}
            })
            
            logger.info(f"Cleaned up {result.deleted_count} old transactions")
            return result.deleted_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup old transactions: {e}")
            return 0
