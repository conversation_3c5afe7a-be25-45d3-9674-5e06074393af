"""
Main bot class for the Telegram Referral Bot
"""

import logging
from telegram import Update, Bo<PERSON>
from telegram.ext import Application, Command<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CallbackQueryHandler, filters
from telegram.error import TelegramError
from typing import Optional

from config import Config
from src.database import Database
from src.handlers.start import StartHandler
from src.handlers.user import UserHandler
from src.handlers.admin import AdminHandler
from src.handlers.callback import CallbackHandler
from src.services.user_service import UserService
from src.services.referral_service import ReferralService
from src.services.transaction_service import TransactionService
from src.services.withdrawal_service import WithdrawalService
from src.services.channel_service import ChannelService
from src.services.product_service import ProductService
from src.utils.logger import setup_logger
from src.utils.security import rate_limiter, ban_manager

logger = setup_logger(__name__)

class ReferralBot:
    """Main bot class"""
    
    def __init__(self, database: Database):
        self.database = database
        self.application: Optional[Application] = None
        
        # Initialize services
        self.user_service = UserService(database)
        self.referral_service = ReferralService(database)
        self.transaction_service = TransactionService(database)
        self.withdrawal_service = WithdrawalService(database)
        self.channel_service = ChannelService(database)
        self.product_service = ProductService(database)
        
        # Initialize handlers
        self.start_handler = StartHandler(self)
        self.user_handler = UserHandler(self)
        self.admin_handler = AdminHandler(self)
        self.callback_handler = CallbackHandler(self)
        
        logger.info("ReferralBot initialized")
    
    async def start(self):
        """Start the bot"""
        try:
            # Create application
            self.application = Application.builder().token(Config.BOT_TOKEN).build()

            # Add handlers
            await self._add_handlers()

            # Initialize bot data
            await self._initialize_bot_data()

            # Start the bot in polling mode
            self.start_polling()

        except Exception as e:
            logger.error(f"Failed to start bot: {e}")
            raise
    
    async def stop(self):
        """Stop the bot"""
        if self.application:
            await self.application.stop()
            logger.info("Bot stopped")
    
    async def _add_handlers(self):
        """Add all command and message handlers"""
        app = self.application
        
        # Command handlers
        app.add_handler(CommandHandler("start", self.start_handler.start_command))
        app.add_handler(CommandHandler("help", self.start_handler.help_command))
        app.add_handler(CommandHandler("admin", self.admin_handler.admin_command))
        app.add_handler(CommandHandler("balance", self.user_handler.balance_command))
        app.add_handler(CommandHandler("referrals", self.user_handler.referrals_command))
        app.add_handler(CommandHandler("withdraw", self.user_handler.withdraw_command))
        app.add_handler(CommandHandler("stats", self.user_handler.stats_command))
        
        # Message handlers
        app.add_handler(MessageHandler(
            filters.TEXT & ~filters.COMMAND,
            self.user_handler.handle_message
        ))
        
        # Callback query handler
        app.add_handler(CallbackQueryHandler(self.callback_handler.handle_callback))
        
        # Error handler
        app.add_error_handler(self._error_handler)
        
        logger.info("All handlers added successfully")
    
    async def _initialize_bot_data(self):
        """Initialize bot data and settings"""
        try:
            # Initialize default products if none exist
            await self.product_service.initialize_default_products()
            
            # Initialize bot settings
            await self._initialize_settings()
            
            logger.info("Bot data initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize bot data: {e}")
    
    async def _initialize_settings(self):
        """Initialize bot settings in database"""
        default_settings = {
            'referral_reward': Config.REFERRAL_REWARD,
            'daily_bonus_amount': Config.DAILY_BONUS_AMOUNT,
            'minimum_withdrawal': Config.MINIMUM_WITHDRAWAL,
            'bot_active': True,
            'maintenance_mode': False,
            'welcome_message': Config.WELCOME_MESSAGE
        }
        
        for key, value in default_settings.items():
            existing = await self.database.settings.find_one({'key': key})
            if not existing:
                await self.database.settings.insert_one({
                    'key': key,
                    'value': value,
                    'updated_at': None
                })
    
    def start_polling(self):
        """Start bot in polling mode"""
        logger.info("Starting bot in long polling mode...")
        logger.info("Bot is running in long polling mode. Press Ctrl+C to stop.")
        logger.info("No webhook, domain, or SSL configuration required.")

        # Start polling with optimal configuration
        self.application.run_polling(
            allowed_updates=Update.ALL_TYPES,
            drop_pending_updates=True,
            poll_interval=1.0,  # Check for updates every second
            timeout=10,         # Timeout for each request
            read_timeout=20,    # Read timeout
            write_timeout=20,   # Write timeout
            connect_timeout=20  # Connection timeout
        )
    
    async def _error_handler(self, update: Update, context):
        """Handle errors"""
        logger.error(f"Update {update} caused error {context.error}")
        
        # Try to send error message to user
        if update and update.effective_chat:
            try:
                await context.bot.send_message(
                    chat_id=update.effective_chat.id,
                    text="❌ An error occurred. Please try again later."
                )
            except TelegramError:
                pass  # Ignore if we can't send the message
    
    async def check_user_permissions(self, user_id: int) -> dict:
        """Check user permissions and restrictions"""
        result = {
            'allowed': True,
            'reason': '',
            'banned': False,
            'rate_limited': False,
            'channels_required': False,
            'missing_channels': []
        }
        
        # Check if user is banned
        user = await self.user_service.get_user(user_id)
        if user and user.is_banned:
            result['allowed'] = False
            result['banned'] = True
            result['reason'] = user.ban_reason or "You are banned from using this bot."
            return result
        
        # Check temporary ban
        if ban_manager.is_temp_banned(user_id):
            remaining = ban_manager.get_ban_time_remaining(user_id)
            result['allowed'] = False
            result['banned'] = True
            result['reason'] = f"You are temporarily banned. Time remaining: {remaining}"
            return result
        
        # Check rate limiting
        if rate_limiter.is_rate_limited(user_id):
            result['allowed'] = False
            result['rate_limited'] = True
            result['reason'] = "You are sending messages too quickly. Please slow down."
            return result
        
        # Check channel subscriptions
        required_channels = await self.channel_service.get_required_channels()
        if required_channels and user:
            missing_channels = []
            for channel in required_channels:
                if not await self.channel_service.is_user_member(user_id, channel.channel_id):
                    missing_channels.append(channel)
            
            if missing_channels:
                result['allowed'] = False
                result['channels_required'] = True
                result['missing_channels'] = missing_channels
                result['reason'] = "You must join all required channels to use this bot."
        
        return result
    
    async def get_bot_info(self) -> dict:
        """Get bot information"""
        try:
            bot_info = await self.application.bot.get_me()
            return {
                'id': bot_info.id,
                'username': bot_info.username,
                'first_name': bot_info.first_name,
                'can_join_groups': bot_info.can_join_groups,
                'can_read_all_group_messages': bot_info.can_read_all_group_messages,
                'supports_inline_queries': bot_info.supports_inline_queries
            }
        except Exception as e:
            logger.error(f"Failed to get bot info: {e}")
            return {}
    
    def get_service(self, service_name: str):
        """Get service by name"""
        services = {
            'user': self.user_service,
            'referral': self.referral_service,
            'transaction': self.transaction_service,
            'withdrawal': self.withdrawal_service,
            'channel': self.channel_service,
            'product': self.product_service
        }
        return services.get(service_name)
