from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, Any, Optional

@dataclass
class AdminSettings:
    """Admin configurable settings for the bot"""
    
    # Financial Settings
    referral_reward: float = 10.0
    daily_bonus_amount: float = 5.0
    minimum_withdrawal: float = 500.0
    withdrawal_cooldown_hours: int = 24
    
    # Task Settings
    task_rewards: Dict[str, float] = field(default_factory=lambda: {
        "join_channel": 25.0,
        "share_bot": 50.0,
        "daily_streak": 100.0,
        "referral_milestone": 150.0
    })
    
    # Bot Settings
    welcome_bonus: float = 0.0
    max_daily_claims: int = 1
    referral_limit_per_day: int = 10
    
    # Admin Info
    updated_by: Optional[int] = None
    updated_at: Optional[datetime] = None
    created_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()
    
    @classmethod
    def from_dict(cls, data: dict) -> 'AdminSettings':
        """Create AdminSettings from dictionary"""
        # Remove MongoDB _id field if present
        data = data.copy()
        data.pop('_id', None)
        
        # Convert datetime strings back to datetime objects
        if 'created_at' in data and isinstance(data['created_at'], str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if 'updated_at' in data and isinstance(data['updated_at'], str):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        
        return cls(**data)
    
    def to_dict(self) -> dict:
        """Convert AdminSettings to dictionary for MongoDB storage"""
        data = {
            'referral_reward': self.referral_reward,
            'daily_bonus_amount': self.daily_bonus_amount,
            'minimum_withdrawal': self.minimum_withdrawal,
            'withdrawal_cooldown_hours': self.withdrawal_cooldown_hours,
            'task_rewards': self.task_rewards,
            'welcome_bonus': self.welcome_bonus,
            'max_daily_claims': self.max_daily_claims,
            'referral_limit_per_day': self.referral_limit_per_day,
            'updated_by': self.updated_by,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
        return data
    
    def update_settings(self, admin_id: int, **kwargs):
        """Update settings with admin tracking"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
        
        self.updated_by = admin_id
        self.updated_at = datetime.utcnow()
