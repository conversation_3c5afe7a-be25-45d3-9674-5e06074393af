#!/usr/bin/env python3
"""
Final Complete Telegram Referral Earning Bot with Full Features
Fixed async event loop issues
"""

import logging
import asyncio
import pytz
from datetime import datetime, timezone, timedelta
from telegram import Update, ReplyKeyboardMarkup, KeyboardButton, InlineKeyboardMarkup, InlineKeyboardButton
from telegram.ext import Application, CommandHandler, MessageHandler, CallbackQueryHandler, filters, ContextTypes

from config import Config
from src.database import Database
from src.models.user import User
from src.models.transaction import Transaction, TransactionType, TransactionStatus
from src.models.referral import Referral, ReferralStatus
from src.services.user_service import UserService
from src.services.referral_service import ReferralService
from src.services.transaction_service import TransactionService
from src.services.withdrawal_service import WithdrawalService
from src.services.channel_service import ChannelService
from src.services.product_service import ProductService
from src.services.admin_settings_service import AdminSettingsService
from src.utils.logger import setup_logger, log_user_action, log_admin_action
from src.utils.security import rate_limiter, ban_manager

# Setup logging
logger = setup_logger(__name__)

class FinalBotApp:
    """Final Complete Telegram Referral Bot Application"""
    
    def __init__(self):
        self.application = None
        self.database = None
        self.services = {}
        self.admin_sessions = {}
        
    async def initialize_async_components(self):
        """Initialize async components"""
        try:
            logger.info("🤖 Starting Complete Telegram Referral Earning Bot...")
            logger.info("🔄 Mode: Long Polling (no webhook/domain required)")
            
            # Validate configuration
            Config.validate_config()
            logger.info("✅ Configuration validated")
            
            # Initialize database
            self.database = Database()
            await self.database.connect()
            logger.info("✅ Database connected successfully")
            
            # Initialize services
            self.services = {
                'user': UserService(self.database),
                'referral': ReferralService(self.database),
                'transaction': TransactionService(self.database),
                'withdrawal': WithdrawalService(self.database),
                'channel': ChannelService(self.database),
                'product': ProductService(self.database),
                'admin_settings': AdminSettingsService(self.database.db)
            }
            logger.info("✅ Services initialized")
            
            # Initialize default products
            try:
                await self.services['product'].initialize_default_products()
                logger.info("✅ Default products initialized")
            except Exception as e:
                logger.warning(f"⚠️ Could not initialize default products: {e}")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize async components: {e}")
            raise

    async def get_setting(self, setting_name: str, default_value=None):
        """Get dynamic admin setting value"""
        try:
            return await self.services['admin_settings'].get_setting_value(setting_name, default_value)
        except Exception as e:
            logger.error(f"Failed to get setting {setting_name}: {e}")
            # Fallback to Config values
            return getattr(Config, setting_name.upper(), default_value)

    def get_main_keyboard(self):
        """Get main menu keyboard with new 5-button layout"""
        keyboard = [
            [KeyboardButton("💰 Balance"), KeyboardButton("🎁 Daily Bonus")],
            [KeyboardButton("📋 Tasks"), KeyboardButton("👥 Referrals")],
            [KeyboardButton("💸 Withdraw")]
        ]
        return ReplyKeyboardMarkup(
            keyboard,
            resize_keyboard=True,
            one_time_keyboard=False,
            input_field_placeholder="Choose your gift adventure! 🎁"
        )
    
    def get_admin_keyboard(self):
        """Get admin menu keyboard"""
        keyboard = [
            [KeyboardButton("👥 Users"), KeyboardButton("💸 Withdrawals")],
            [KeyboardButton("📢 Channels"), KeyboardButton("🛍️ Products")],
            [KeyboardButton("📊 Analytics"), KeyboardButton("⚙️ Bot Settings")],
            [KeyboardButton("📨 Broadcast"), KeyboardButton("🔙 Back to User")]
        ]
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)
    
    async def check_user_permissions(self, user_id: int) -> dict:
        """Check user permissions and restrictions"""
        result = {
            'allowed': True,
            'reason': '',
            'banned': False,
            'rate_limited': False
        }
        
        # Check if user is banned
        try:
            user = await self.services['user'].get_user(user_id)
            if user and user.is_banned:
                result['allowed'] = False
                result['banned'] = True
                result['reason'] = user.ban_reason or "You are banned from using this bot."
                return result
        except:
            pass  # Continue if user service fails
        
        # Check temporary ban
        if ban_manager.is_temp_banned(user_id):
            remaining = ban_manager.get_ban_time_remaining(user_id)
            result['allowed'] = False
            result['banned'] = True
            result['reason'] = f"You are temporarily banned. Time remaining: {remaining}"
            return result
        
        # Check rate limiting
        if rate_limiter.is_rate_limited(user_id):
            result['allowed'] = False
            result['rate_limited'] = True
            result['reason'] = "You are sending messages too quickly. Please slow down."
            return result
        
        return result
    
    def extract_referral_code(self, text: str):
        """Extract referral code from start command"""
        if not text or not text.startswith('/start'):
            return None
        
        parts = text.split()
        if len(parts) > 1:
            code = parts[1].strip()
            if len(code) == 8 and code.isalnum():
                return code.upper()
        
        return None
    
    # ==================== COMMAND HANDLERS ====================
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command with animated welcome sequence"""
        try:
            user = update.effective_user

            # Check user permissions
            permissions = await self.check_user_permissions(user.id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            # Start the animated welcome sequence
            await self._animated_welcome_sequence(update, context, user)

        except Exception as e:
            logger.error(f"Error in start command: {e}")
            await update.message.reply_text("❌ An error occurred. Please try again.")

    async def _animated_welcome_sequence(self, update: Update, context: ContextTypes.DEFAULT_TYPE, user):
        """Create fast animated welcome sequence for gifts bot"""
        try:
            # Extract referral code from command
            referral_code = None
            if context.args:
                referral_code = self.extract_referral_code(f"/start {context.args[0]}")

            # Step 1: Initial loading message
            loading_msg = await update.message.reply_text(
                self._get_loading_frame_1()
            )

            await asyncio.sleep(0.15)

            # Step 2: Quick progress animation
            await loading_msg.edit_text(self._get_loading_frame(2))
            await asyncio.sleep(0.15)

            await loading_msg.edit_text(self._get_loading_frame(3))
            await asyncio.sleep(0.15)

            # Create or get user during setup
            user_data = {
                'user_id': user.id,
                'username': user.username,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'language_code': user.language_code,
                'is_bot': user.is_bot,
                'is_premium': getattr(user, 'is_premium', False)
            }

            try:
                db_user = await self.services['user'].create_user(user_data, referral_code)
            except Exception as e:
                logger.error(f"Failed to create user: {e}")
                db_user = None

            # Step 3: Features loading
            await loading_msg.edit_text(self._get_features_loading_frame())
            await asyncio.sleep(0.2)

            # Process referral if applicable
            if referral_code and db_user and db_user.referred_by:
                await self._process_referral(db_user.user_id, db_user.referred_by, referral_code)

            # Step 4: Success reveal
            await loading_msg.edit_text(self._get_welcome_reveal_frame())
            await asyncio.sleep(0.2)

            # Step 5: Final gifts bot welcome
            final_welcome = self._get_final_welcome_message(user, db_user, referral_code)

            await loading_msg.edit_text(
                final_welcome,
                parse_mode='Markdown'
            )

            # Send the interactive keyboard
            await context.bot.send_message(
                chat_id=update.effective_chat.id,
                text="🎁 **Pick your next gift adventure!**",
                reply_markup=self.get_main_keyboard(),
                parse_mode='Markdown'
            )

            log_user_action(user.id, "START_COMMAND", f"Referral: {referral_code}")

        except Exception as e:
            logger.error(f"Error in animated welcome: {e}")
            # Fallback to simple welcome
            await update.message.reply_text(
                "🎁 Welcome to the Gifts Bot! 🎉\n\nStart earning amazing gifts now!",
                reply_markup=self.get_main_keyboard()
            )
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        try:
            user_id = update.effective_user.id
            
            # Check user permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return
            
            help_text = f"""
🎁 **GIFTS BOT HELP** 🎁

**How to earn gifts:**
✨ **Daily Gift:** Get ₹{Config.DAILY_BONUS_AMOUNT} every day!
👥 **Share & Earn:** Get ₹{Config.REFERRAL_REWARD} per friend!
🛍️ **Claim Gifts:** Canva Pro, Spotify, Netflix!

**Super easy steps:**
1. Share your link with friends
2. Claim your daily gift
3. Get amazing rewards!

**Available gifts:**
🎨 Canva Pro • 🎵 Spotify • 📺 Netflix & more!

Need help? Just ask! We're here for you! 🌟
            """
            
            await update.message.reply_text(
                help_text,
                parse_mode='Markdown',
                reply_markup=self.get_main_keyboard()
            )
            
            log_user_action(user_id, "HELP_COMMAND")
            
        except Exception as e:
            logger.error(f"Error in help command: {e}")
            await update.message.reply_text("❌ An error occurred. Please try again.")
    
    async def balance_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle balance command with comprehensive user profile display"""
        try:
            user_id = update.effective_user.id
            telegram_user = update.effective_user

            # Check permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            # Show loading animation
            loading_msg = await update.message.reply_text(
                "👤 **Loading Your Profile...**\n\n✨ Gathering your information!\n▓▓░░░ 50%"
            )

            await asyncio.sleep(0.15)
            await loading_msg.edit_text(
                "👤 **Loading Your Profile...**\n\n🎉 Almost ready!\n▓▓▓▓▓ 100%",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.1)

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user: {e}")
                user = None

            if not user:
                profile_text = """
👤 **USER PROFILE** 👤

❌ **Profile not found**
Please start the bot with /start to create your profile.
                """
            else:
                # Get today's earnings (transactions from today in IST)
                ist_tz = pytz.timezone('Asia/Kolkata')
                today_ist = datetime.now(ist_tz).date()
                today_start_ist = ist_tz.localize(datetime.combine(today_ist, datetime.min.time()))
                today_start_utc = today_start_ist.astimezone(timezone.utc)

                try:
                    # Get today's transactions
                    today_transactions = await self.services['transaction'].get_user_transactions_since(
                        user_id, today_start_utc
                    )
                    today_earnings = sum(t.amount for t in today_transactions if t.amount > 0)
                except:
                    today_earnings = 0.0

                # Format join date
                join_date = user.created_at.strftime("%d %b %Y") if user.created_at else "Unknown"

                # Account status
                status_icon = "🎉" if user.balance >= Config.MINIMUM_WITHDRAWAL else "📈"
                status_text = "Ready for gifts!" if user.balance >= Config.MINIMUM_WITHDRAWAL else "Keep earning!"

                # Premium status
                premium_icon = "⭐" if telegram_user.is_premium else "🆓"
                premium_text = "Premium User" if telegram_user.is_premium else "Free User"

                profile_text = f"""
👤 **USER PROFILE** 👤

┌─────────────────────────────────────┐
│              📋 ACCOUNT INFO        │
├─────────────────────────────────────┤
│ 👤 **Name:** {user.get_display_name()}
│ 🆔 **Username:** @{telegram_user.username or 'Not set'}
│ 🔢 **User ID:** {user_id}
│ 📅 **Joined:** {join_date}
│ {premium_icon} **Status:** {premium_text}
│ {status_icon} **Account:** {status_text}
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│              💰 FINANCIAL INFO      │
├─────────────────────────────────────┤
│ 💎 **Current Balance:** ₹{user.balance:.2f}
│ 📈 **Today's Earnings:** ₹{today_earnings:.2f}
│ 🌟 **Total Earned:** ₹{user.total_earned:.2f}
│ 🎊 **Total Withdrawn:** ₹{user.total_withdrawals:.2f}
│ 💸 **Withdrawal Count:** {user.withdrawal_count}
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│              👥 REFERRAL STATS      │
├─────────────────────────────────────┤
│ 🔗 **Referral Code:** {user.referral_code}
│ 👥 **Total Referrals:** {user.total_referrals}
│ ✅ **Successful Refs:** {user.successful_referrals}
│ 💰 **Referral Earnings:** ₹{user.successful_referrals * Config.REFERRAL_REWARD:.2f}
└─────────────────────────────────────┘

🎯 **Quick Actions:**
• Share your link to earn ₹{Config.REFERRAL_REWARD} per friend
• Claim daily bonus of ₹{Config.DAILY_BONUS_AMOUNT}
• Minimum withdrawal: ₹{Config.MINIMUM_WITHDRAWAL}
                """

            await loading_msg.edit_text(
                profile_text,
                parse_mode='Markdown'
            )

            log_user_action(user_id, "PROFILE_VIEWED")

        except Exception as e:
            logger.error(f"Error in balance command: {e}")
            await update.message.reply_text("❌ Failed to load profile information.")

    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle text messages"""
        try:
            user_id = update.effective_user.id
            message_text = update.message.text

            # Check user permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            # Handle main menu buttons
            if message_text == "💰 Balance":
                await self.balance_command(update, context)
            elif message_text == "🎁 Daily Bonus":
                await self._handle_daily_bonus(update, context)
            elif message_text == "📋 Tasks":
                await self._handle_tasks_menu(update, context)
            elif message_text == "👥 Referrals":
                await self._handle_referrals_menu(update, context)
            elif message_text == "💸 Withdraw":
                await self._handle_withdraw_menu(update, context)
            elif message_text == "📊 Statistics":
                await self._handle_stats_menu(update, context)
            elif message_text == "⚙️ Settings":
                await self._handle_settings_menu(update, context)
            elif message_text == "❓ Help & Support":
                await self.help_command(update, context)
            else:
                await update.message.reply_text(
                    "🎁 Use the gift buttons below to start earning! ✨",
                    reply_markup=self.get_main_keyboard()
                )

        except Exception as e:
            logger.error(f"Error handling message: {e}")
            await update.message.reply_text("❌ An error occurred. Please try again.")

    async def _handle_daily_bonus(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle daily bonus claiming with enhanced 2-3 second animation"""
        try:
            user_id = update.effective_user.id

            # Enhanced checking animation sequence
            checking_msg = await update.message.reply_text(
                "🎁 **DAILY GIFT SCANNER** 🎁\n\n🔍 Scanning for gifts...\n░░░░░ 0%"
            )

            await asyncio.sleep(0.3)
            await checking_msg.edit_text(
                "🎁 **DAILY GIFT SCANNER** 🎁\n\n🔍 Scanning for gifts...\n▓░░░░ 20%",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.3)
            await checking_msg.edit_text(
                "🎁 **DAILY GIFT SCANNER** 🎁\n\n🎯 Gift detected!\n▓▓░░░ 40%",
                parse_mode='Markdown'
            )

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user for daily bonus: {e}")
                user = None

            if not user:
                await checking_msg.edit_text("❌ User not found. Please start the bot with /start")
                return

            await asyncio.sleep(0.3)
            await checking_msg.edit_text(
                "🎁 **DAILY GIFT SCANNER** 🎁\n\n✨ Verifying eligibility...\n▓▓▓░░ 60%",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.3)
            await checking_msg.edit_text(
                "🎁 **DAILY GIFT SCANNER** 🎁\n\n🎉 Verification complete!\n▓▓▓▓░ 80%",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.3)
            await checking_msg.edit_text(
                "🎁 **DAILY GIFT SCANNER** 🎁\n\n🚀 Ready to claim!\n▓▓▓▓▓ 100%",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.2)

            if not user.can_claim_daily_bonus():
                # Get next claim time in IST
                next_claim_ist = user.get_next_daily_bonus_time_ist()

                if next_claim_ist:
                    # Calculate time remaining until next 12:00 AM IST
                    ist_tz = pytz.timezone('Asia/Kolkata')
                    now_ist = datetime.now(ist_tz)
                    remaining = next_claim_ist - now_ist

                    hours = int(remaining.total_seconds() // 3600)
                    minutes = int((remaining.total_seconds() % 3600) // 60)

                    cooldown_text = f"""
✨ **DAILY GIFT** ✨

🎁 **You already claimed today's bonus!**

⏰ **Next gift at:** 12:00 AM IST
🕐 **Time remaining:** {hours}h {minutes}m
💎 **Daily gift:** ₹{Config.DAILY_BONUS_AMOUNT}

🌟 Come back tomorrow for your next gift!
                    """
                else:
                    cooldown_text = """
✨ **DAILY GIFT** ✨

🎁 **Gift not ready yet!**

Come back later for your daily gift! 🌟
                    """

                await checking_msg.edit_text(cooldown_text, parse_mode='Markdown')
                return

            # Enhanced claiming animation sequence
            await checking_msg.edit_text(
                "🎁 **GIFT CLAIMING INITIATED** 🎁\n\n🔄 Processing your gift...\n💫 ✨ 💫",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.4)
            await checking_msg.edit_text(
                "🎁 **GIFT CLAIMING INITIATED** 🎁\n\n💰 Adding to your wallet...\n🌟 ✨ 🌟 ✨ 🌟",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.4)
            await checking_msg.edit_text(
                "🎁 **GIFT CLAIMING INITIATED** 🎁\n\n🎉 Almost there...\n🎊 🎉 🎊 🎉 🎊 🎉",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.3)

            # Get dynamic daily bonus amount
            daily_bonus_amount = await self.get_setting('daily_bonus_amount', Config.DAILY_BONUS_AMOUNT)

            # Claim daily bonus
            user.claim_daily_bonus(daily_bonus_amount)
            await self.services['user'].update_user(user)

            # Create transaction record
            try:
                await self.services['transaction'].create_transaction(
                    user_id=user_id,
                    amount=daily_bonus_amount,
                    transaction_type=TransactionType.DAILY_BONUS,
                    description="Daily bonus claimed"
                )
            except Exception as e:
                logger.error(f"Failed to create transaction: {e}")

            # Get dynamic referral reward for display
            referral_reward = await self.get_setting('referral_reward', Config.REFERRAL_REWARD)

            # Final celebratory success message
            bonus_text = f"""
🎊 **CONGRATULATIONS!** 🎊

🎁 **DAILY GIFT CLAIMED SUCCESSFULLY!** 🎁

┌─────────────────────────────────────┐
│              🎉 REWARD DETAILS      │
├─────────────────────────────────────┤
│ 💰 **Gift Amount:** +₹{daily_bonus_amount}
│ 💎 **New Balance:** ₹{user.balance:.2f}
│ 🕐 **Next Gift:** Tomorrow at 12:00 AM IST
└─────────────────────────────────────┘

🌟 **Amazing!** You're building your gift collection!

🚀 **Want more gifts? Here's how:**
• 👥 Share your referral link with friends
• 💰 Earn ₹{referral_reward} for each friend who joins
• 🎁 Claim daily gifts every day

Keep up the great work! 🎯
            """

            await checking_msg.edit_text(
                bonus_text,
                parse_mode='Markdown'
            )

            log_user_action(user_id, "DAILY_BONUS_CLAIMED", f"Amount: {daily_bonus_amount}")

        except Exception as e:
            logger.error(f"Error in daily bonus: {e}")
            await update.message.reply_text("❌ Failed to claim daily bonus.")

    async def _handle_referrals_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle enhanced referrals menu with comprehensive display"""
        try:
            user_id = update.effective_user.id

            # Show loading animation
            loading_msg = await update.message.reply_text(
                "👥 **Loading Referral Info...**\n\n🔍 Gathering your data!\n▓▓░░░ 50%"
            )

            await asyncio.sleep(0.15)
            await loading_msg.edit_text(
                "👥 **Loading Referral Info...**\n\n🎉 Almost ready!\n▓▓▓▓▓ 100%",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.1)

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user for referrals: {e}")
                user = None

            if not user:
                # Fallback referral link
                referral_link = f"https://t.me/{Config.BOT_USERNAME}?start=REF{user_id}"
                referral_count = 0
                referral_earnings = 0.0
                total_referrals = 0
            else:
                referral_link = f"https://t.me/{Config.BOT_USERNAME}?start={user.referral_code}"
                try:
                    referral_count = await self.services['referral'].get_successful_referral_count(user_id)
                    referral_earnings = await self.services['referral'].get_referral_earnings(user_id)
                    total_referrals = user.total_referrals
                except Exception as e:
                    logger.error(f"Failed to get referral stats: {e}")
                    referral_count = 0
                    referral_earnings = 0.0
                    total_referrals = 0

            # Calculate potential earnings
            potential_earnings = referral_count * Config.REFERRAL_REWARD
            success_rate = (referral_count / total_referrals * 100) if total_referrals > 0 else 0

            referral_text = f"""
👥 **REFERRAL PROGRAM** 👥

┌─────────────────────────────────────┐
│              🔗 YOUR LINK           │
├─────────────────────────────────────┤
│ `{referral_link}`
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│              📊 STATISTICS          │
├─────────────────────────────────────┤
│ 👥 **Total Referrals:** {total_referrals}
│ ✅ **Successful Refs:** {referral_count}
│ 📈 **Success Rate:** {success_rate:.1f}%
│ 💰 **Reward per Friend:** ₹{Config.REFERRAL_REWARD}
│ 🎁 **Total Earned:** ₹{referral_earnings:.2f}
│ 💎 **Potential Earnings:** ₹{potential_earnings:.2f}
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│              💡 HOW IT WORKS        │
├─────────────────────────────────────┤
│ 1️⃣ Share your unique link
│ 2️⃣ Friends join using your link
│ 3️⃣ You earn ₹{Config.REFERRAL_REWARD} per friend!
│ 4️⃣ Instant rewards to your balance
└─────────────────────────────────────┘

🚀 **Pro Tips for More Referrals:**
• 📱 Share in WhatsApp groups
• 💬 Post in Telegram channels
• 🎯 Add personal messages
• 🎁 Mention the daily gifts feature
• 💰 Highlight easy earning potential

🌟 **Start sharing and watch your gifts grow!**
            """

            # Create enhanced inline keyboard for referral actions
            keyboard = [
                [InlineKeyboardButton("📋 Copy Link", callback_data="copy_referral_link"),
                 InlineKeyboardButton("📤 Share Link", callback_data="share_referral_link")],
                [InlineKeyboardButton("📊 My Referrals", callback_data="my_referrals"),
                 InlineKeyboardButton("🏆 Leaderboard", callback_data="referral_leaderboard")],
                [InlineKeyboardButton("🔄 Refresh Stats", callback_data="refresh_referrals")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await loading_msg.edit_text(
                referral_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

            log_user_action(user_id, "REFERRALS_VIEWED")

        except Exception as e:
            logger.error(f"Error in referrals menu: {e}")
            await update.message.reply_text("❌ Failed to get referral information.")

    async def _handle_tasks_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle tasks menu with admin-configurable tasks system"""
        try:
            user_id = update.effective_user.id

            # Show loading animation
            loading_msg = await update.message.reply_text(
                "📋 **Loading Tasks...**\n\n🔍 Finding available tasks!\n▓▓░░░ 50%"
            )

            await asyncio.sleep(0.15)
            await loading_msg.edit_text(
                "📋 **Loading Tasks...**\n\n🎯 Tasks ready!\n▓▓▓▓▓ 100%",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.1)

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user for tasks: {e}")
                user = None

            if not user:
                await loading_msg.edit_text("❌ User not found. Please start the bot with /start")
                return

            # Define admin-configurable tasks (in a real implementation, these would come from database)
            available_tasks = [
                {
                    "id": "join_channel",
                    "title": "Join Our Channel",
                    "description": "Join our official Telegram channel",
                    "reward": 25.0,
                    "type": "channel_join",
                    "requirement": "@your_channel",
                    "completed": False  # This would be checked against user's completed tasks
                },
                {
                    "id": "share_bot",
                    "title": "Share Bot with Friends",
                    "description": "Share the bot in 3 different groups",
                    "reward": 50.0,
                    "type": "share_task",
                    "requirement": "3 shares",
                    "completed": False
                },
                {
                    "id": "daily_streak",
                    "title": "7-Day Login Streak",
                    "description": "Login and claim daily bonus for 7 consecutive days",
                    "reward": 100.0,
                    "type": "streak_task",
                    "requirement": "7 days",
                    "completed": False
                },
                {
                    "id": "referral_milestone",
                    "title": "Referral Champion",
                    "description": "Successfully refer 5 friends",
                    "reward": 150.0,
                    "type": "referral_task",
                    "requirement": "5 referrals",
                    "completed": user.successful_referrals >= 5 if user else False
                }
            ]

            # Calculate total available rewards
            total_available = sum(task["reward"] for task in available_tasks if not task["completed"])
            completed_count = sum(1 for task in available_tasks if task["completed"])

            tasks_text = f"""
📋 **TASKS CENTER** 📋

┌─────────────────────────────────────┐
│              📊 OVERVIEW            │
├─────────────────────────────────────┤
│ 📝 **Total Tasks:** {len(available_tasks)}
│ ✅ **Completed:** {completed_count}
│ ⏳ **Remaining:** {len(available_tasks) - completed_count}
│ 💰 **Available Rewards:** ₹{total_available:.2f}
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│              🎯 AVAILABLE TASKS     │
├─────────────────────────────────────┤
"""

            # Add each task to the display
            for i, task in enumerate(available_tasks, 1):
                status_icon = "✅" if task["completed"] else "⏳"
                status_text = "COMPLETED" if task["completed"] else "AVAILABLE"

                tasks_text += f"""│ {status_icon} **{task['title']}**
│    💰 Reward: ₹{task['reward']} | {status_text}
│    📝 {task['description']}
│    🎯 Requirement: {task['requirement']}
│
"""

            tasks_text += """└─────────────────────────────────────┘

🌟 **Complete tasks to earn extra gifts!**
💡 **Tip:** Tasks refresh regularly with new opportunities!

🚀 **Ready to start earning?** Choose a task below:"""

            # Create inline keyboard for tasks
            keyboard = []
            for task in available_tasks:
                if not task["completed"]:
                    button_text = f"🎯 {task['title']} (+₹{task['reward']})"
                    callback_data = f"task_{task['id']}"
                    keyboard.append([InlineKeyboardButton(button_text, callback_data=callback_data)])

            # Add additional buttons
            keyboard.append([InlineKeyboardButton("🔄 Refresh Tasks", callback_data="refresh_tasks")])
            keyboard.append([InlineKeyboardButton("📊 Task History", callback_data="task_history")])

            reply_markup = InlineKeyboardMarkup(keyboard)

            await loading_msg.edit_text(
                tasks_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

            log_user_action(user_id, "TASKS_VIEWED", f"Available: {len(available_tasks) - completed_count}")

        except Exception as e:
            logger.error(f"Error in tasks menu: {e}")
            await update.message.reply_text("❌ Failed to load tasks.")

    async def admin_settings_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Admin command to view and manage bot settings"""
        try:
            user_id = update.effective_user.id

            # Check if user is admin
            if user_id not in Config.ADMIN_USER_IDS:
                await update.message.reply_text("❌ Access denied. Admin only command.")
                return

            # Get current settings
            settings = await self.services['admin_settings'].get_settings()

            settings_text = f"""
🔧 **ADMIN SETTINGS PANEL** 🔧

┌─────────────────────────────────────┐
│              💰 FINANCIAL SETTINGS  │
├─────────────────────────────────────┤
│ 💰 **Referral Reward:** ₹{settings.referral_reward}
│ 🎁 **Daily Bonus:** ₹{settings.daily_bonus_amount}
│ 💸 **Min Withdrawal:** ₹{settings.minimum_withdrawal}
│ ⏰ **Withdrawal Cooldown:** {settings.withdrawal_cooldown_hours}h
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│              🎯 TASK REWARDS        │
├─────────────────────────────────────┤
│ 📺 **Join Channel:** ₹{settings.task_rewards.get('join_channel', 25)}
│ 📤 **Share Bot:** ₹{settings.task_rewards.get('share_bot', 50)}
│ 🔥 **Daily Streak:** ₹{settings.task_rewards.get('daily_streak', 100)}
│ 👥 **Referral Milestone:** ₹{settings.task_rewards.get('referral_milestone', 150)}
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│              ⚙️ BOT SETTINGS        │
├─────────────────────────────────────┤
│ 🎉 **Welcome Bonus:** ₹{settings.welcome_bonus}
│ 📅 **Max Daily Claims:** {settings.max_daily_claims}
│ 👥 **Referral Limit/Day:** {settings.referral_limit_per_day}
└─────────────────────────────────────┘

📝 **Last Updated:** {settings.updated_at.strftime('%d %b %Y %H:%M') if settings.updated_at else 'Never'}
👤 **Updated By:** Admin ID {settings.updated_by if settings.updated_by else 'System'}

🔧 **Commands:**
• `/set_referral_reward <amount>` - Set referral reward
• `/set_daily_bonus <amount>` - Set daily bonus
• `/set_min_withdrawal <amount>` - Set minimum withdrawal
• `/reset_settings` - Reset to defaults
            """

            await update.message.reply_text(settings_text, parse_mode='Markdown')
            log_user_action(user_id, "ADMIN_SETTINGS_VIEWED")

        except Exception as e:
            logger.error(f"Error in admin settings: {e}")
            await update.message.reply_text("❌ Failed to load admin settings.")

    async def set_referral_reward_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Admin command to set referral reward amount"""
        try:
            user_id = update.effective_user.id

            if user_id not in Config.ADMIN_USER_IDS:
                await update.message.reply_text("❌ Access denied. Admin only command.")
                return

            if not context.args or len(context.args) != 1:
                await update.message.reply_text("❌ Usage: /set_referral_reward <amount>\nExample: /set_referral_reward 15")
                return

            try:
                amount = float(context.args[0])
                if amount < 0:
                    await update.message.reply_text("❌ Amount must be positive.")
                    return

                success = await self.services['admin_settings'].update_settings(
                    admin_id=user_id,
                    referral_reward=amount
                )

                if success:
                    await update.message.reply_text(f"✅ Referral reward updated to ₹{amount}")
                    log_admin_action(user_id, "REFERRAL_REWARD_UPDATED", f"New amount: {amount}")
                else:
                    await update.message.reply_text("❌ Failed to update setting.")

            except ValueError:
                await update.message.reply_text("❌ Invalid amount. Please enter a valid number.")

        except Exception as e:
            logger.error(f"Error setting referral reward: {e}")
            await update.message.reply_text("❌ Failed to update referral reward.")

    async def _handle_withdraw_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle withdraw menu with 4-5 second cool animation"""
        try:
            user_id = update.effective_user.id

            # Start the epic 4-5 second withdrawal animation
            withdraw_msg = await update.message.reply_text(
                "💸 **WITHDRAWAL SYSTEM INITIALIZING** 💸\n\n🔄 Starting secure connection...\n░░░░░ 0%"
            )

            await asyncio.sleep(0.5)
            await withdraw_msg.edit_text(
                "💸 **WITHDRAWAL SYSTEM INITIALIZING** 💸\n\n🔐 Establishing secure tunnel...\n▓░░░░ 20%",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.5)
            await withdraw_msg.edit_text(
                "💸 **WITHDRAWAL SYSTEM INITIALIZING** 💸\n\n🛡️ Verifying security protocols...\n▓▓░░░ 40%",
                parse_mode='Markdown'
            )

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user for withdrawal: {e}")
                user = None

            if not user:
                await withdraw_msg.edit_text("❌ User not found. Please start the bot with /start")
                return

            await asyncio.sleep(0.5)
            await withdraw_msg.edit_text(
                "💸 **WITHDRAWAL SYSTEM INITIALIZING** 💸\n\n💰 Accessing your balance...\n▓▓▓░░ 60%",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.5)
            await withdraw_msg.edit_text(
                "💸 **WITHDRAWAL SYSTEM INITIALIZING** 💸\n\n🎯 Calculating withdrawal options...\n▓▓▓▓░ 80%",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.5)
            await withdraw_msg.edit_text(
                "💸 **WITHDRAWAL SYSTEM INITIALIZING** 💸\n\n✅ System ready!\n▓▓▓▓▓ 100%",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.5)
            await withdraw_msg.edit_text(
                "💸 **WITHDRAWAL SYSTEM READY** 💸\n\n🚀 Preparing your options...\n💎 ✨ 💎 ✨ 💎",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.5)

            # Get dynamic minimum withdrawal amount
            minimum_withdrawal = await self.get_setting('minimum_withdrawal', Config.MINIMUM_WITHDRAWAL)
            referral_reward = await self.get_setting('referral_reward', Config.REFERRAL_REWARD)
            daily_bonus_amount = await self.get_setting('daily_bonus_amount', Config.DAILY_BONUS_AMOUNT)

            # Check balance against minimum withdrawal limit
            can_withdraw = user.balance >= minimum_withdrawal
            needed_amount = minimum_withdrawal - user.balance if not can_withdraw else 0

            if can_withdraw:
                withdraw_text = f"""
💸 **WITHDRAWAL CENTER** 💸

┌─────────────────────────────────────┐
│              💰 YOUR BALANCE        │
├─────────────────────────────────────┤
│ 💎 **Current Balance:** ₹{user.balance:.2f}
│ ✅ **Status:** Ready for withdrawal!
│ 🎯 **Minimum Required:** ₹{minimum_withdrawal}
│ 🎉 **Available to Withdraw:** ₹{user.balance:.2f}
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│              🎁 WITHDRAWAL OPTIONS  │
├─────────────────────────────────────┤
│ 🛍️ **Digital Products & Vouchers**
│ 💰 **Direct Cash Transfer**
│ 🎮 **Gaming Credits & Recharges**
│ 🛒 **E-commerce Gift Cards**
└─────────────────────────────────────┘

🌟 **Congratulations!** You've earned enough for withdrawal!
Choose your preferred option below:
                """

                # Create inline keyboard for withdrawal options
                keyboard = [
                    [InlineKeyboardButton("🛍️ Digital Products", callback_data="withdraw_products"),
                     InlineKeyboardButton("💰 Cash Transfer", callback_data="withdraw_cash")],
                    [InlineKeyboardButton("🎮 Gaming Credits", callback_data="withdraw_gaming"),
                     InlineKeyboardButton("🛒 Gift Cards", callback_data="withdraw_cards")],
                    [InlineKeyboardButton("📋 Withdrawal History", callback_data="withdrawal_history")]
                ]
            else:
                withdraw_text = f"""
💸 **WITHDRAWAL CENTER** 💸

┌─────────────────────────────────────┐
│              💰 YOUR BALANCE        │
├─────────────────────────────────────┤
│ 💎 **Current Balance:** ₹{user.balance:.2f}
│ ⏳ **Status:** Almost there!
│ 🎯 **Minimum Required:** ₹{minimum_withdrawal}
│ 📈 **Need:** ₹{needed_amount:.2f} more
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│              🚀 EARN MORE GIFTS     │
├─────────────────────────────────────┤
│ 🎁 **Daily Bonus:** ₹{daily_bonus_amount}/day
│ 👥 **Referrals:** ₹{referral_reward}/friend
│ 📋 **Tasks:** Complete for rewards
│ 🎯 **Goal:** Just ₹{needed_amount:.2f} away!
└─────────────────────────────────────┘

💪 **You're so close!** Keep earning to unlock withdrawals!

🎯 **Quick ways to reach ₹{minimum_withdrawal}:**
• Invite {int(needed_amount/referral_reward) + 1} friends = ₹{(int(needed_amount/referral_reward) + 1) * referral_reward}
• Claim daily bonus for {int(needed_amount/daily_bonus_amount) + 1} days
• Complete available tasks
                """

                # Create inline keyboard for earning options
                keyboard = [
                    [InlineKeyboardButton("👥 Invite Friends", callback_data="referral_menu"),
                     InlineKeyboardButton("🎁 Daily Bonus", callback_data="daily_bonus")],
                    [InlineKeyboardButton("📋 View Tasks", callback_data="tasks_menu")],
                    [InlineKeyboardButton("📊 Withdrawal History", callback_data="withdrawal_history")]
                ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await withdraw_msg.edit_text(
                withdraw_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

            log_user_action(user_id, "WITHDRAW_MENU_VIEWED", f"Balance: {user.balance}, Can withdraw: {can_withdraw}")

        except Exception as e:
            logger.error(f"Error in withdraw menu: {e}")
            await update.message.reply_text("❌ Failed to access withdrawal options.")

    async def _handle_stats_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle stats menu"""
        try:
            user_id = update.effective_user.id

            try:
                stats = await self.services['user'].get_user_statistics(user_id)
            except Exception as e:
                logger.error(f"Failed to get user stats: {e}")
                stats = None

            if not stats:
                stats_text = """
📊 **Your Statistics**

👤 **Profile:**
• User ID: `{user_id}`
• Joined: Recently

💰 **Earnings:**
• Current Balance: ₹0.00
• Total Earned: ₹0.00

👥 **Referrals:**
• Successful Referrals: 0

📈 **Activity:**
• Account Status: ✅ Active
                """.format(user_id=user_id)
            else:
                user_data = stats['user']
                join_date = datetime.fromisoformat(user_data['created_at'].replace('Z', '+00:00')).strftime('%d/%m/%Y')

                stats_text = f"""
📊 **Your Statistics**

👤 **Profile:**
• Name: {user_data.get('first_name', 'N/A')}
• Username: @{user_data.get('username', 'N/A')}
• User ID: `{user_data['user_id']}`
• Joined: {join_date}

💰 **Earnings:**
• Current Balance: ₹{user_data['balance']:.2f}
• Total Earned: ₹{user_data['total_earned']:.2f}
• Total Withdrawals: ₹{user_data.get('total_withdrawals', 0):.2f}

👥 **Referrals:**
• Successful Referrals: {stats['referral_count']}
• Referral Earnings: ₹{stats['referral_count'] * Config.REFERRAL_REWARD:.2f}

📈 **Activity:**
• Total Transactions: {stats['transaction_count']}
• Account Status: {'✅ Active' if user_data['is_active'] else '❌ Inactive'}
                """

            await update.message.reply_text(
                stats_text,
                parse_mode='Markdown'
            )

            log_user_action(user_id, "STATS_VIEWED")

        except Exception as e:
            logger.error(f"Error in stats menu: {e}")
            await update.message.reply_text("❌ Failed to get statistics.")

    async def _handle_settings_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle settings menu"""
        try:
            user_id = update.effective_user.id

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user for settings: {e}")
                user = None

            if not user:
                settings_text = f"""
⚙️ **Account Settings**

**Profile Information:**
• User ID: `{user_id}`
• Status: ✅ Active

**Referral Code:** `REF{user_id}`

Use the buttons below to manage your settings:
                """
            else:
                settings_text = f"""
⚙️ **Account Settings**

**Profile Information:**
• Name: {user.get_display_name()}
• Username: @{user.username or 'N/A'}
• User ID: `{user.user_id}`
• Language: {user.language_code or 'N/A'}

**Account Status:**
• Status: {'✅ Active' if user.is_active else '❌ Inactive'}
• Premium: {'✅ Yes' if user.is_premium else '❌ No'}
• Joined: {user.created_at.strftime('%d/%m/%Y')}

**Referral Code:** `{user.referral_code}`

Use the buttons below to manage your settings:
                """

            # Create inline keyboard for settings
            keyboard = [
                [InlineKeyboardButton("🔔 Notifications", callback_data="settings_notifications")],
                [InlineKeyboardButton("🌐 Language", callback_data="settings_language")],
                [InlineKeyboardButton("📱 Account Info", callback_data="settings_account")],
                [InlineKeyboardButton("❓ Help", callback_data="settings_help")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                settings_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

            log_user_action(user_id, "SETTINGS_VIEWED")

        except Exception as e:
            logger.error(f"Error in settings menu: {e}")
            await update.message.reply_text("❌ Failed to access settings.")

    # ==================== CALLBACK HANDLERS ====================

    async def handle_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle callback queries from inline keyboards"""
        try:
            query = update.callback_query
            user_id = query.from_user.id
            data = query.data

            await query.answer()

            # Check user permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await query.edit_message_text(permissions['reason'])
                return

            # Route callback based on data
            if data.startswith("balance_"):
                await self._handle_balance_callbacks(query, context, data)
            elif data.startswith("copy_referral_link"):
                await self._copy_referral_link(query, context)
            elif data.startswith("withdraw_"):
                await self._handle_withdrawal_callbacks(query, context, data)
            elif data.startswith("settings_"):
                await self._handle_settings_callbacks(query, context, data)
            else:
                await query.edit_message_text("🚧 Feature coming soon...")

        except Exception as e:
            logger.error(f"Error handling callback: {e}")
            try:
                await query.answer("❌ An error occurred.")
            except:
                pass

    async def _handle_balance_callbacks(self, query, context, data):
        """Handle balance-related callbacks"""
        try:
            user_id = query.from_user.id
            action = data.replace("balance_", "")

            if action == "refresh":
                await self._refresh_balance(query, context)
            else:
                await query.edit_message_text("🚧 Feature coming soon...")

        except Exception as e:
            logger.error(f"Error in balance callback: {e}")

    async def _refresh_balance(self, query, context):
        """Refresh and show current balance"""
        try:
            user_id = query.from_user.id

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user for balance refresh: {e}")
                user = None

            if not user:
                balance_text = """
💰 **Your Balance**

Current Balance: **₹0.00**
Total Earned: **₹0.00**
Total Withdrawals: **₹0.00**

📈 Start referring friends to earn!
                """
            else:
                balance_text = f"""
💰 **Your Balance**

Current Balance: **₹{user.balance:.2f}**
Total Earned: **₹{user.total_earned:.2f}**
Total Withdrawals: **₹{user.total_withdrawals:.2f}**

"""

                if user.balance >= Config.MINIMUM_WITHDRAWAL:
                    balance_text += f"✅ You can withdraw! (Min: ₹{Config.MINIMUM_WITHDRAWAL})"
                else:
                    needed = Config.MINIMUM_WITHDRAWAL - user.balance
                    balance_text += f"📈 Earn ₹{needed:.2f} more to withdraw!"

            # Create inline keyboard for balance actions
            keyboard = [
                [InlineKeyboardButton("📈 Transaction History", callback_data="balance_history")],
                [InlineKeyboardButton("💸 Withdraw", callback_data="withdraw_menu")],
                [InlineKeyboardButton("🔄 Refresh", callback_data="balance_refresh")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                balance_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error refreshing balance: {e}")

    async def _copy_referral_link(self, query, context):
        """Show referral link for copying"""
        try:
            user_id = query.from_user.id

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user for referral link: {e}")
                user = None

            if not user:
                referral_link = f"https://t.me/{Config.BOT_USERNAME}?start=REF{user_id}"
            else:
                referral_link = f"https://t.me/{Config.BOT_USERNAME}?start={user.referral_code}"

            copy_text = f"""
📋 **Your Referral Link**

`{referral_link}`

**How to share:**
1. Copy the link above
2. Share with friends on social media
3. Earn ₹{Config.REFERRAL_REWARD} for each successful referral!

**Tips:**
• Share in groups and channels
• Add a personal message
• Explain the benefits
            """

            await query.edit_message_text(
                copy_text,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Error copying referral link: {e}")

    async def _handle_withdrawal_callbacks(self, query, context, data):
        """Handle withdrawal-related callbacks"""
        await query.edit_message_text("🚧 Withdrawal features coming soon...")

    async def _handle_settings_callbacks(self, query, context, data):
        """Handle settings-related callbacks"""
        await query.edit_message_text("🚧 Settings features coming soon...")

    # ==================== HELPER METHODS ====================

    async def _process_referral(self, referred_user_id: int, referrer_user_id: int, referral_code: str):
        """Process a new referral"""
        try:
            # Create referral record
            referral = await self.services['referral'].create_referral(
                referrer_user_id,
                referred_user_id,
                referral_code
            )

            if referral:
                # Complete the referral and give reward
                success = await self.services['referral'].complete_referral(referral.referral_id)

                if success:
                    # Notify referrer
                    try:
                        referrer = await self.services['user'].get_user(referrer_user_id)
                        if referrer:
                            notification_text = f"""
🎉 **New Referral!**

You earned ₹{Config.REFERRAL_REWARD} for referring a new user!

💰 Your new balance: ₹{referrer.balance:.2f}
👥 Total referrals: {referrer.referral_count}

Keep sharing your referral link to earn more! 🚀
                            """

                            await self.application.bot.send_message(
                                chat_id=referrer_user_id,
                                text=notification_text,
                                parse_mode='Markdown'
                            )
                    except Exception as e:
                        logger.error(f"Failed to notify referrer {referrer_user_id}: {e}")

                logger.info(f"Processed referral: {referrer_user_id} -> {referred_user_id}")

        except Exception as e:
            logger.error(f"Error processing referral: {e}")

    # ==================== ANIMATION FRAMES ====================

    def _get_loading_frame_1(self):
        """Initial loading frame"""
        return """
🎁 **GIFTS BOT STARTING...**

✨ Preparing amazing gifts for you!
▓░░░░ 25%
        """

    def _get_loading_frame(self, step):
        """Progressive loading frames"""
        frames = {
            2: {
                'progress': '▓▓░░░ 50%',
                'status': '🎁 Loading gift catalog...'
            },
            3: {
                'progress': '▓▓▓░░ 75%',
                'status': '🌟 Setting up rewards...'
            }
        }

        frame_data = frames.get(step, frames[3])

        return f"""
🎁 **GIFTS BOT STARTING...**

{frame_data['status']}
{frame_data['progress']}
        """

    def _get_features_loading_frame(self):
        """Features loading frame"""
        return """
🎁 **GIFTS BOT READY!**

🎊 Your gift account is ready!
▓▓▓▓▓ 100%
        """

    def _get_welcome_reveal_frame(self):
        """Welcome reveal frame"""
        return """
🎉 **WELCOME TO GIFTS BOT!** 🎉

✨ Start earning amazing gifts now!
🎁 Canva Pro • Spotify • Netflix & more!
        """

    def _get_final_welcome_message(self, user, db_user, referral_code):
        """Generate final welcome message with gifts bot branding"""
        user_name = db_user.get_display_name() if db_user else user.first_name

        # Referral bonus message
        referral_bonus_msg = ""
        if referral_code:
            referral_bonus_msg = f"🎊 **BONUS!** You joined through a friend! Both of you get extra gifts! 🎁\n\n"

        # Main welcome content
        welcome_content = f"""
🎁 **WELCOME TO GIFTS BOT, {user_name.upper()}!** 🎁

{referral_bonus_msg}🌟 **EARN AMAZING GIFTS:**
• ✨ Daily Gift: ₹{Config.DAILY_BONUS_AMOUNT} every day!
• 👥 Share & Earn: ₹{Config.REFERRAL_REWARD} per friend!
• 🛍️ Claim Gifts: Canva Pro, Spotify, Netflix!

🚀 **HOW IT WORKS:**
Share your link → Friends join → You both get gifts!

🎯 **READY TO START?**
Tap the buttons below and start earning! 🎉
        """

        return welcome_content

    def _get_final_welcome_message(self, user, db_user, referral_code):
        """Generate final welcome message with branding"""
        user_name = db_user.get_display_name() if db_user else user.first_name

        # Create ASCII art logo
        logo = """
╔═══════════════════════════════════════╗
║  ██████╗ ██████╗  ██████╗             ║
║  ██╔══██╗██╔══██╗██╔═══██╗            ║
║  ██████╔╝██████╔╝██║   ██║            ║
║  ██╔═══╝ ██╔══██╗██║   ██║            ║
║  ██║     ██║  ██║╚██████╔╝            ║
║  ╚═╝     ╚═╝  ╚═╝ ╚═════╝             ║
║                                       ║
║     🎯 REFERRAL EARNING BOT 🎯        ║
╚═══════════════════════════════════════╝
        """

        # Referral bonus message
        referral_bonus_msg = ""
        if referral_code:
            referral_bonus_msg = f"""
🎊 **REFERRAL BONUS ACTIVATED!**
You joined through a referral link!
Both you and your referrer will earn rewards! 🎁
            """

        # Main welcome content
        welcome_content = f"""
{logo}

🌟 **Welcome, {user_name}!** 🌟

{referral_bonus_msg}

┌─────────────────────────────────────┐
│           💰 EARNING SYSTEM         │
├─────────────────────────────────────┤
│ 🎁 Daily Bonus:     ₹{Config.DAILY_BONUS_AMOUNT}           │
│ 👥 Per Referral:    ₹{Config.REFERRAL_REWARD}          │
│ 💸 Min Withdrawal:  ₹{Config.MINIMUM_WITHDRAWAL}         │
└─────────────────────────────────────┘

🚀 **GET STARTED:**
• Share your referral link with friends
• Claim daily bonuses every 24 hours
• Withdraw earnings as digital products
• Track your progress with analytics

💎 **PREMIUM FEATURES:**
✅ Real-time balance tracking
✅ Instant referral rewards
✅ Professional support
✅ Secure transactions
✅ Multiple withdrawal options

🎯 **Ready to start earning?**
Use the menu below to explore all features!
        """

        return welcome_content

    # ==================== ERROR HANDLER ====================

    async def error_handler(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle errors"""
        logger.error(f"Update {update} caused error {context.error}")

        # Try to send error message to user
        if update and update.effective_chat:
            try:
                await context.bot.send_message(
                    chat_id=update.effective_chat.id,
                    text="❌ An error occurred. Please try again later."
                )
            except:
                pass  # Ignore if we can't send the message

# ==================== MAIN EXECUTION ====================

def main():
    """Main function to run the complete bot"""
    try:
        logger.info("🤖 Starting Complete Telegram Referral Earning Bot...")
        logger.info("🔄 Mode: Long Polling (no webhook/domain required)")

        # Validate configuration
        Config.validate_config()
        logger.info("✅ Configuration validated")

        # Create bot instance
        bot = FinalBotApp()

        # Create application
        application = Application.builder().token(Config.BOT_TOKEN).build()
        bot.application = application

        # Add handlers
        application.add_handler(CommandHandler("start", bot.start_command))
        application.add_handler(CommandHandler("help", bot.help_command))
        application.add_handler(CommandHandler("balance", bot.balance_command))

        # Admin commands
        application.add_handler(CommandHandler("admin_settings", bot.admin_settings_command))
        application.add_handler(CommandHandler("set_referral_reward", bot.set_referral_reward_command))

        application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, bot.handle_message))
        application.add_handler(CallbackQueryHandler(bot.handle_callback))
        application.add_error_handler(bot.error_handler)

        logger.info("✅ All handlers added successfully")

        # Initialize async components in a separate task
        async def init_and_run():
            await bot.initialize_async_components()
            logger.info("🚀 Starting bot in long polling mode...")
            logger.info("✅ Bot is running! Press Ctrl+C to stop.")
            logger.info("ℹ️ No webhook, domain, or SSL configuration required.")
            logger.info(f"🤖 Bot username: @{Config.BOT_USERNAME}")

        # Run the initialization
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(init_and_run())

        # Run the bot
        application.run_polling(
            allowed_updates=Update.ALL_TYPES,
            drop_pending_updates=True
        )

    except KeyboardInterrupt:
        logger.info("👋 Bot stopped by user")
    except Exception as e:
        logger.error(f"❌ Bot error: {e}")
        raise

if __name__ == "__main__":
    main()
