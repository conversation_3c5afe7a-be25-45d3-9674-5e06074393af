#!/usr/bin/env python3
"""
Final Complete Telegram Referral Earning Bot with Full Features
Fixed async event loop issues
"""

import logging
import asyncio
from datetime import datetime, timezone, timedelta
from telegram import Update, ReplyKeyboardMarkup, KeyboardButton, InlineKeyboardMarkup, InlineKeyboardButton
from telegram.ext import Application, CommandHandler, MessageHandler, CallbackQueryHandler, filters, ContextTypes

from config import Config
from src.database import Database
from src.models.user import User
from src.models.transaction import Transaction, TransactionType, TransactionStatus
from src.models.referral import Referral, ReferralStatus
from src.services.user_service import UserService
from src.services.referral_service import ReferralService
from src.services.transaction_service import TransactionService
from src.services.withdrawal_service import WithdrawalService
from src.services.channel_service import ChannelService
from src.services.product_service import ProductService
from src.utils.logger import setup_logger, log_user_action, log_admin_action
from src.utils.security import rate_limiter, ban_manager

# Setup logging
logger = setup_logger(__name__)

class FinalBotApp:
    """Final Complete Telegram Referral Bot Application"""
    
    def __init__(self):
        self.application = None
        self.database = None
        self.services = {}
        self.admin_sessions = {}
        
    async def initialize_async_components(self):
        """Initialize async components"""
        try:
            logger.info("🤖 Starting Complete Telegram Referral Earning Bot...")
            logger.info("🔄 Mode: Long Polling (no webhook/domain required)")
            
            # Validate configuration
            Config.validate_config()
            logger.info("✅ Configuration validated")
            
            # Initialize database
            self.database = Database()
            await self.database.connect()
            logger.info("✅ Database connected successfully")
            
            # Initialize services
            self.services = {
                'user': UserService(self.database),
                'referral': ReferralService(self.database),
                'transaction': TransactionService(self.database),
                'withdrawal': WithdrawalService(self.database),
                'channel': ChannelService(self.database),
                'product': ProductService(self.database)
            }
            logger.info("✅ Services initialized")
            
            # Initialize default products
            try:
                await self.services['product'].initialize_default_products()
                logger.info("✅ Default products initialized")
            except Exception as e:
                logger.warning(f"⚠️ Could not initialize default products: {e}")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize async components: {e}")
            raise
    
    def get_main_keyboard(self):
        """Get main menu keyboard with original button names"""
        keyboard = [
            [KeyboardButton("💰 Balance"), KeyboardButton("🎁 Daily Bonus")],
            [KeyboardButton("💸 Withdraw"), KeyboardButton("👥 Referrals")],
            [KeyboardButton("📊 Statistics"), KeyboardButton("⚙️ Settings")],
            [KeyboardButton("❓ Help & Support")]
        ]
        return ReplyKeyboardMarkup(
            keyboard,
            resize_keyboard=True,
            one_time_keyboard=False,
            input_field_placeholder="Choose your gift adventure! 🎁"
        )
    
    def get_admin_keyboard(self):
        """Get admin menu keyboard"""
        keyboard = [
            [KeyboardButton("👥 Users"), KeyboardButton("💸 Withdrawals")],
            [KeyboardButton("📢 Channels"), KeyboardButton("🛍️ Products")],
            [KeyboardButton("📊 Analytics"), KeyboardButton("⚙️ Bot Settings")],
            [KeyboardButton("📨 Broadcast"), KeyboardButton("🔙 Back to User")]
        ]
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)
    
    async def check_user_permissions(self, user_id: int) -> dict:
        """Check user permissions and restrictions"""
        result = {
            'allowed': True,
            'reason': '',
            'banned': False,
            'rate_limited': False
        }
        
        # Check if user is banned
        try:
            user = await self.services['user'].get_user(user_id)
            if user and user.is_banned:
                result['allowed'] = False
                result['banned'] = True
                result['reason'] = user.ban_reason or "You are banned from using this bot."
                return result
        except:
            pass  # Continue if user service fails
        
        # Check temporary ban
        if ban_manager.is_temp_banned(user_id):
            remaining = ban_manager.get_ban_time_remaining(user_id)
            result['allowed'] = False
            result['banned'] = True
            result['reason'] = f"You are temporarily banned. Time remaining: {remaining}"
            return result
        
        # Check rate limiting
        if rate_limiter.is_rate_limited(user_id):
            result['allowed'] = False
            result['rate_limited'] = True
            result['reason'] = "You are sending messages too quickly. Please slow down."
            return result
        
        return result
    
    def extract_referral_code(self, text: str):
        """Extract referral code from start command"""
        if not text or not text.startswith('/start'):
            return None
        
        parts = text.split()
        if len(parts) > 1:
            code = parts[1].strip()
            if len(code) == 8 and code.isalnum():
                return code.upper()
        
        return None
    
    # ==================== COMMAND HANDLERS ====================
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command with animated welcome sequence"""
        try:
            user = update.effective_user

            # Check user permissions
            permissions = await self.check_user_permissions(user.id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            # Start the animated welcome sequence
            await self._animated_welcome_sequence(update, context, user)

        except Exception as e:
            logger.error(f"Error in start command: {e}")
            await update.message.reply_text("❌ An error occurred. Please try again.")

    async def _animated_welcome_sequence(self, update: Update, context: ContextTypes.DEFAULT_TYPE, user):
        """Create fast animated welcome sequence for gifts bot"""
        try:
            # Extract referral code from command
            referral_code = None
            if context.args:
                referral_code = self.extract_referral_code(f"/start {context.args[0]}")

            # Step 1: Initial loading message
            loading_msg = await update.message.reply_text(
                self._get_loading_frame_1()
            )

            await asyncio.sleep(0.15)

            # Step 2: Quick progress animation
            await loading_msg.edit_text(self._get_loading_frame(2))
            await asyncio.sleep(0.15)

            await loading_msg.edit_text(self._get_loading_frame(3))
            await asyncio.sleep(0.15)

            # Create or get user during setup
            user_data = {
                'user_id': user.id,
                'username': user.username,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'language_code': user.language_code,
                'is_bot': user.is_bot,
                'is_premium': getattr(user, 'is_premium', False)
            }

            try:
                db_user = await self.services['user'].create_user(user_data, referral_code)
            except Exception as e:
                logger.error(f"Failed to create user: {e}")
                db_user = None

            # Step 3: Features loading
            await loading_msg.edit_text(self._get_features_loading_frame())
            await asyncio.sleep(0.2)

            # Process referral if applicable
            if referral_code and db_user and db_user.referred_by:
                await self._process_referral(db_user.user_id, db_user.referred_by, referral_code)

            # Step 4: Success reveal
            await loading_msg.edit_text(self._get_welcome_reveal_frame())
            await asyncio.sleep(0.2)

            # Step 5: Final gifts bot welcome
            final_welcome = self._get_final_welcome_message(user, db_user, referral_code)

            await loading_msg.edit_text(
                final_welcome,
                parse_mode='Markdown'
            )

            # Send the interactive keyboard
            await context.bot.send_message(
                chat_id=update.effective_chat.id,
                text="🎁 **Pick your next gift adventure!**",
                reply_markup=self.get_main_keyboard(),
                parse_mode='Markdown'
            )

            log_user_action(user.id, "START_COMMAND", f"Referral: {referral_code}")

        except Exception as e:
            logger.error(f"Error in animated welcome: {e}")
            # Fallback to simple welcome
            await update.message.reply_text(
                "🎁 Welcome to the Gifts Bot! 🎉\n\nStart earning amazing gifts now!",
                reply_markup=self.get_main_keyboard()
            )
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        try:
            user_id = update.effective_user.id
            
            # Check user permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return
            
            help_text = f"""
🎁 **GIFTS BOT HELP** 🎁

**How to earn gifts:**
✨ **Daily Gift:** Get ₹{Config.DAILY_BONUS_AMOUNT} every day!
👥 **Share & Earn:** Get ₹{Config.REFERRAL_REWARD} per friend!
🛍️ **Claim Gifts:** Canva Pro, Spotify, Netflix!

**Super easy steps:**
1. Share your link with friends
2. Claim your daily gift
3. Get amazing rewards!

**Available gifts:**
🎨 Canva Pro • 🎵 Spotify • 📺 Netflix & more!

Need help? Just ask! We're here for you! 🌟
            """
            
            await update.message.reply_text(
                help_text,
                parse_mode='Markdown',
                reply_markup=self.get_main_keyboard()
            )
            
            log_user_action(user_id, "HELP_COMMAND")
            
        except Exception as e:
            logger.error(f"Error in help command: {e}")
            await update.message.reply_text("❌ An error occurred. Please try again.")
    
    async def balance_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle balance command with loading animation"""
        try:
            user_id = update.effective_user.id

            # Check permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            # Show loading animation
            loading_msg = await update.message.reply_text(
                "🎁 **Loading Your Gifts...**\n\n✨ Checking your rewards!\n▓▓░░░ 50%"
            )

            await asyncio.sleep(0.15)
            await loading_msg.edit_text(
                "🎁 **Loading Your Gifts...**\n\n🎉 Almost ready!\n▓▓▓▓▓ 100%",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.1)

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user: {e}")
                user = None

            if not user:
                balance_text = """
🎁 **YOUR GIFT WALLET** 🎁

💎 **Gift Balance:** ₹0.00
🌟 **Total Gifts Earned:** ₹0.00
🎊 **Gifts Claimed:** ₹0.00

✨ Start sharing to earn amazing gifts!
                """
            else:
                status_icon = "🎉" if user.balance >= Config.MINIMUM_WITHDRAWAL else "📈"
                status_text = f"Ready to claim gifts!" if user.balance >= Config.MINIMUM_WITHDRAWAL else f"Earn ₹{Config.MINIMUM_WITHDRAWAL - user.balance:.2f} more for gifts!"

                balance_text = f"""
🎁 **YOUR GIFT WALLET** 🎁

💎 **Gift Balance:** ₹{user.balance:.2f}
🌟 **Total Gifts Earned:** ₹{user.total_earned:.2f}
🎊 **Gifts Claimed:** ₹{user.total_withdrawals:.2f}

{status_icon} **{status_text}**
                """

            # Create inline keyboard for gift actions
            keyboard = [
                [InlineKeyboardButton("📈 Gift History", callback_data="balance_history")],
                [InlineKeyboardButton("🛍️ Claim Gifts Now", callback_data="withdraw_menu")],
                [InlineKeyboardButton("🔄 Refresh", callback_data="balance_refresh")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await loading_msg.edit_text(
                balance_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

            log_user_action(user_id, "BALANCE_CHECKED")

        except Exception as e:
            logger.error(f"Error in balance command: {e}")
            await update.message.reply_text("❌ Failed to get balance information.")

    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle text messages"""
        try:
            user_id = update.effective_user.id
            message_text = update.message.text

            # Check user permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            # Handle main menu buttons
            if message_text == "💰 Balance":
                await self.balance_command(update, context)
            elif message_text == "🎁 Daily Bonus":
                await self._handle_daily_bonus(update, context)
            elif message_text == "💸 Withdraw":
                await self._handle_withdraw_menu(update, context)
            elif message_text == "👥 Referrals":
                await self._handle_referrals_menu(update, context)
            elif message_text == "📊 Statistics":
                await self._handle_stats_menu(update, context)
            elif message_text == "⚙️ Settings":
                await self._handle_settings_menu(update, context)
            elif message_text == "❓ Help & Support":
                await self.help_command(update, context)
            else:
                await update.message.reply_text(
                    "🎁 Use the gift buttons below to start earning! ✨",
                    reply_markup=self.get_main_keyboard()
                )

        except Exception as e:
            logger.error(f"Error handling message: {e}")
            await update.message.reply_text("❌ An error occurred. Please try again.")

    async def _handle_daily_bonus(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle daily bonus claiming with animation"""
        try:
            user_id = update.effective_user.id

            # Show checking animation
            checking_msg = await update.message.reply_text(
                "✨ **Daily Gift Check!**\n\n🎁 Checking your gift...\n▓▓░░░ 50%"
            )

            await asyncio.sleep(0.15)

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user for daily bonus: {e}")
                user = None

            if not user:
                await checking_msg.edit_text("❌ User not found. Please start the bot with /start")
                return

            await checking_msg.edit_text(
                "✨ **Daily Gift Check!**\n\n🎉 Gift ready!\n▓▓▓▓▓ 100%",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.1)

            if not user.can_claim_daily_bonus():
                # Calculate time remaining
                if user.last_daily_bonus:
                    next_claim = user.last_daily_bonus.replace(tzinfo=timezone.utc) + timedelta(hours=24)
                    remaining = next_claim - datetime.now(timezone.utc)
                    hours = int(remaining.total_seconds() // 3600)
                    minutes = int((remaining.total_seconds() % 3600) // 60)

                    cooldown_text = f"""
✨ **DAILY GIFT** ✨

🎁 **Already claimed today!**

⏰ **Next gift in:** {hours}h {minutes}m
💎 **Daily gift:** ₹{Config.DAILY_BONUS_AMOUNT}

🌟 Come back tomorrow for your next gift!
                    """
                else:
                    cooldown_text = """
✨ **DAILY GIFT** ✨

🎁 **Gift not ready yet!**

Come back later for your daily gift! 🌟
                    """

                await checking_msg.edit_text(cooldown_text, parse_mode='Markdown')
                return

            # Show claiming animation
            await checking_msg.edit_text(
                "🎁 **CLAIMING YOUR GIFT!**\n\n✨ Adding to your wallet!\n🎉🎉🎉",
                parse_mode='Markdown'
            )

            await asyncio.sleep(0.2)

            # Claim daily bonus
            user.claim_daily_bonus(Config.DAILY_BONUS_AMOUNT)
            await self.services['user'].update_user(user)

            # Create transaction record
            try:
                await self.services['transaction'].create_transaction(
                    user_id=user_id,
                    amount=Config.DAILY_BONUS_AMOUNT,
                    transaction_type=TransactionType.DAILY_BONUS,
                    description="Daily bonus claimed"
                )
            except Exception as e:
                logger.error(f"Failed to create transaction: {e}")

            # Final success message
            bonus_text = f"""
🎉 **GIFT CLAIMED!** 🎉

💎 **You got:** +₹{Config.DAILY_BONUS_AMOUNT}
🎁 **New balance:** ₹{user.balance:.2f}

✨ **Awesome!** Come back tomorrow for another gift!

🚀 **Earn more gifts:**
• Share your link with friends!
• Get ₹{Config.REFERRAL_REWARD} per friend who joins!
            """

            await checking_msg.edit_text(
                bonus_text,
                parse_mode='Markdown'
            )

            log_user_action(user_id, "DAILY_BONUS_CLAIMED", f"Amount: {Config.DAILY_BONUS_AMOUNT}")

        except Exception as e:
            logger.error(f"Error in daily bonus: {e}")
            await update.message.reply_text("❌ Failed to claim daily bonus.")

    async def _handle_referrals_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle referrals menu"""
        try:
            user_id = update.effective_user.id

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user for referrals: {e}")
                user = None

            if not user:
                # Fallback referral link
                referral_link = f"https://t.me/{Config.BOT_USERNAME}?start=REF{user_id}"
                referral_count = 0
                referral_earnings = 0.0
            else:
                referral_link = f"https://t.me/{Config.BOT_USERNAME}?start={user.referral_code}"
                try:
                    referral_count = await self.services['referral'].get_successful_referral_count(user_id)
                    referral_earnings = await self.services['referral'].get_referral_earnings(user_id)
                except Exception as e:
                    logger.error(f"Failed to get referral stats: {e}")
                    referral_count = 0
                    referral_earnings = 0.0

            referral_text = f"""
👥 **Your Referral Program**

🔗 Your Referral Link:
`{referral_link}`

📊 **Statistics:**
• Successful Referrals: **{referral_count}**
• Earned from Referrals: **₹{referral_earnings:.2f}**
• Reward per Referral: **₹{Config.REFERRAL_REWARD}**

💡 **How it works:**
1. Share your link with friends
2. They join using your link
3. You earn ₹{Config.REFERRAL_REWARD} per referral!

**Tips:**
• Share in groups and channels
• Add a personal message
• Explain the benefits
            """

            # Create inline keyboard for referral actions
            keyboard = [
                [InlineKeyboardButton("📋 Copy Link", callback_data="copy_referral_link")],
                [InlineKeyboardButton("📊 My Referrals", callback_data="my_referrals")],
                [InlineKeyboardButton("🏆 Leaderboard", callback_data="referral_leaderboard")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                referral_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

            log_user_action(user_id, "REFERRALS_VIEWED")

        except Exception as e:
            logger.error(f"Error in referrals menu: {e}")
            await update.message.reply_text("❌ Failed to get referral information.")

    async def _handle_withdraw_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle withdraw menu"""
        try:
            user_id = update.effective_user.id

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user for withdrawal: {e}")
                user = None

            if not user:
                await update.message.reply_text("❌ User not found. Please start the bot with /start")
                return

            withdraw_text = f"""
💸 **Withdrawal Options**

Your Balance: **₹{user.balance:.2f}**

Choose your withdrawal option:
            """

            # Create inline keyboard for withdrawal options
            keyboard = []
            if user.balance >= Config.MINIMUM_WITHDRAWAL:
                keyboard.append([InlineKeyboardButton("🛍️ Digital Products", callback_data="withdraw_products")])
                keyboard.append([InlineKeyboardButton("💰 Cash Withdrawal", callback_data="withdraw_cash")])
            else:
                needed = Config.MINIMUM_WITHDRAWAL - user.balance
                keyboard.append([InlineKeyboardButton(f"❌ Need ₹{needed:.2f} more", callback_data="insufficient_balance")])

            keyboard.append([InlineKeyboardButton("📋 Withdrawal History", callback_data="withdrawal_history")])
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                withdraw_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

            log_user_action(user_id, "WITHDRAW_MENU_VIEWED")

        except Exception as e:
            logger.error(f"Error in withdraw menu: {e}")
            await update.message.reply_text("❌ Failed to access withdrawal options.")

    async def _handle_stats_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle stats menu"""
        try:
            user_id = update.effective_user.id

            try:
                stats = await self.services['user'].get_user_statistics(user_id)
            except Exception as e:
                logger.error(f"Failed to get user stats: {e}")
                stats = None

            if not stats:
                stats_text = """
📊 **Your Statistics**

👤 **Profile:**
• User ID: `{user_id}`
• Joined: Recently

💰 **Earnings:**
• Current Balance: ₹0.00
• Total Earned: ₹0.00

👥 **Referrals:**
• Successful Referrals: 0

📈 **Activity:**
• Account Status: ✅ Active
                """.format(user_id=user_id)
            else:
                user_data = stats['user']
                join_date = datetime.fromisoformat(user_data['created_at'].replace('Z', '+00:00')).strftime('%d/%m/%Y')

                stats_text = f"""
📊 **Your Statistics**

👤 **Profile:**
• Name: {user_data.get('first_name', 'N/A')}
• Username: @{user_data.get('username', 'N/A')}
• User ID: `{user_data['user_id']}`
• Joined: {join_date}

💰 **Earnings:**
• Current Balance: ₹{user_data['balance']:.2f}
• Total Earned: ₹{user_data['total_earned']:.2f}
• Total Withdrawals: ₹{user_data.get('total_withdrawals', 0):.2f}

👥 **Referrals:**
• Successful Referrals: {stats['referral_count']}
• Referral Earnings: ₹{stats['referral_count'] * Config.REFERRAL_REWARD:.2f}

📈 **Activity:**
• Total Transactions: {stats['transaction_count']}
• Account Status: {'✅ Active' if user_data['is_active'] else '❌ Inactive'}
                """

            await update.message.reply_text(
                stats_text,
                parse_mode='Markdown'
            )

            log_user_action(user_id, "STATS_VIEWED")

        except Exception as e:
            logger.error(f"Error in stats menu: {e}")
            await update.message.reply_text("❌ Failed to get statistics.")

    async def _handle_settings_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle settings menu"""
        try:
            user_id = update.effective_user.id

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user for settings: {e}")
                user = None

            if not user:
                settings_text = f"""
⚙️ **Account Settings**

**Profile Information:**
• User ID: `{user_id}`
• Status: ✅ Active

**Referral Code:** `REF{user_id}`

Use the buttons below to manage your settings:
                """
            else:
                settings_text = f"""
⚙️ **Account Settings**

**Profile Information:**
• Name: {user.get_display_name()}
• Username: @{user.username or 'N/A'}
• User ID: `{user.user_id}`
• Language: {user.language_code or 'N/A'}

**Account Status:**
• Status: {'✅ Active' if user.is_active else '❌ Inactive'}
• Premium: {'✅ Yes' if user.is_premium else '❌ No'}
• Joined: {user.created_at.strftime('%d/%m/%Y')}

**Referral Code:** `{user.referral_code}`

Use the buttons below to manage your settings:
                """

            # Create inline keyboard for settings
            keyboard = [
                [InlineKeyboardButton("🔔 Notifications", callback_data="settings_notifications")],
                [InlineKeyboardButton("🌐 Language", callback_data="settings_language")],
                [InlineKeyboardButton("📱 Account Info", callback_data="settings_account")],
                [InlineKeyboardButton("❓ Help", callback_data="settings_help")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                settings_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

            log_user_action(user_id, "SETTINGS_VIEWED")

        except Exception as e:
            logger.error(f"Error in settings menu: {e}")
            await update.message.reply_text("❌ Failed to access settings.")

    # ==================== CALLBACK HANDLERS ====================

    async def handle_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle callback queries from inline keyboards"""
        try:
            query = update.callback_query
            user_id = query.from_user.id
            data = query.data

            await query.answer()

            # Check user permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await query.edit_message_text(permissions['reason'])
                return

            # Route callback based on data
            if data.startswith("balance_"):
                await self._handle_balance_callbacks(query, context, data)
            elif data.startswith("copy_referral_link"):
                await self._copy_referral_link(query, context)
            elif data.startswith("withdraw_"):
                await self._handle_withdrawal_callbacks(query, context, data)
            elif data.startswith("settings_"):
                await self._handle_settings_callbacks(query, context, data)
            else:
                await query.edit_message_text("🚧 Feature coming soon...")

        except Exception as e:
            logger.error(f"Error handling callback: {e}")
            try:
                await query.answer("❌ An error occurred.")
            except:
                pass

    async def _handle_balance_callbacks(self, query, context, data):
        """Handle balance-related callbacks"""
        try:
            user_id = query.from_user.id
            action = data.replace("balance_", "")

            if action == "refresh":
                await self._refresh_balance(query, context)
            else:
                await query.edit_message_text("🚧 Feature coming soon...")

        except Exception as e:
            logger.error(f"Error in balance callback: {e}")

    async def _refresh_balance(self, query, context):
        """Refresh and show current balance"""
        try:
            user_id = query.from_user.id

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user for balance refresh: {e}")
                user = None

            if not user:
                balance_text = """
💰 **Your Balance**

Current Balance: **₹0.00**
Total Earned: **₹0.00**
Total Withdrawals: **₹0.00**

📈 Start referring friends to earn!
                """
            else:
                balance_text = f"""
💰 **Your Balance**

Current Balance: **₹{user.balance:.2f}**
Total Earned: **₹{user.total_earned:.2f}**
Total Withdrawals: **₹{user.total_withdrawals:.2f}**

"""

                if user.balance >= Config.MINIMUM_WITHDRAWAL:
                    balance_text += f"✅ You can withdraw! (Min: ₹{Config.MINIMUM_WITHDRAWAL})"
                else:
                    needed = Config.MINIMUM_WITHDRAWAL - user.balance
                    balance_text += f"📈 Earn ₹{needed:.2f} more to withdraw!"

            # Create inline keyboard for balance actions
            keyboard = [
                [InlineKeyboardButton("📈 Transaction History", callback_data="balance_history")],
                [InlineKeyboardButton("💸 Withdraw", callback_data="withdraw_menu")],
                [InlineKeyboardButton("🔄 Refresh", callback_data="balance_refresh")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                balance_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error refreshing balance: {e}")

    async def _copy_referral_link(self, query, context):
        """Show referral link for copying"""
        try:
            user_id = query.from_user.id

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user for referral link: {e}")
                user = None

            if not user:
                referral_link = f"https://t.me/{Config.BOT_USERNAME}?start=REF{user_id}"
            else:
                referral_link = f"https://t.me/{Config.BOT_USERNAME}?start={user.referral_code}"

            copy_text = f"""
📋 **Your Referral Link**

`{referral_link}`

**How to share:**
1. Copy the link above
2. Share with friends on social media
3. Earn ₹{Config.REFERRAL_REWARD} for each successful referral!

**Tips:**
• Share in groups and channels
• Add a personal message
• Explain the benefits
            """

            await query.edit_message_text(
                copy_text,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Error copying referral link: {e}")

    async def _handle_withdrawal_callbacks(self, query, context, data):
        """Handle withdrawal-related callbacks"""
        await query.edit_message_text("🚧 Withdrawal features coming soon...")

    async def _handle_settings_callbacks(self, query, context, data):
        """Handle settings-related callbacks"""
        await query.edit_message_text("🚧 Settings features coming soon...")

    # ==================== HELPER METHODS ====================

    async def _process_referral(self, referred_user_id: int, referrer_user_id: int, referral_code: str):
        """Process a new referral"""
        try:
            # Create referral record
            referral = await self.services['referral'].create_referral(
                referrer_user_id,
                referred_user_id,
                referral_code
            )

            if referral:
                # Complete the referral and give reward
                success = await self.services['referral'].complete_referral(referral.referral_id)

                if success:
                    # Notify referrer
                    try:
                        referrer = await self.services['user'].get_user(referrer_user_id)
                        if referrer:
                            notification_text = f"""
🎉 **New Referral!**

You earned ₹{Config.REFERRAL_REWARD} for referring a new user!

💰 Your new balance: ₹{referrer.balance:.2f}
👥 Total referrals: {referrer.referral_count}

Keep sharing your referral link to earn more! 🚀
                            """

                            await self.application.bot.send_message(
                                chat_id=referrer_user_id,
                                text=notification_text,
                                parse_mode='Markdown'
                            )
                    except Exception as e:
                        logger.error(f"Failed to notify referrer {referrer_user_id}: {e}")

                logger.info(f"Processed referral: {referrer_user_id} -> {referred_user_id}")

        except Exception as e:
            logger.error(f"Error processing referral: {e}")

    # ==================== ANIMATION FRAMES ====================

    def _get_loading_frame_1(self):
        """Initial loading frame"""
        return """
🎁 **GIFTS BOT STARTING...**

✨ Preparing amazing gifts for you!
▓░░░░ 25%
        """

    def _get_loading_frame(self, step):
        """Progressive loading frames"""
        frames = {
            2: {
                'progress': '▓▓░░░ 50%',
                'status': '🎁 Loading gift catalog...'
            },
            3: {
                'progress': '▓▓▓░░ 75%',
                'status': '🌟 Setting up rewards...'
            }
        }

        frame_data = frames.get(step, frames[3])

        return f"""
🎁 **GIFTS BOT STARTING...**

{frame_data['status']}
{frame_data['progress']}
        """

    def _get_features_loading_frame(self):
        """Features loading frame"""
        return """
🎁 **GIFTS BOT READY!**

🎊 Your gift account is ready!
▓▓▓▓▓ 100%
        """

    def _get_welcome_reveal_frame(self):
        """Welcome reveal frame"""
        return """
🎉 **WELCOME TO GIFTS BOT!** 🎉

✨ Start earning amazing gifts now!
🎁 Canva Pro • Spotify • Netflix & more!
        """

    def _get_final_welcome_message(self, user, db_user, referral_code):
        """Generate final welcome message with gifts bot branding"""
        user_name = db_user.get_display_name() if db_user else user.first_name

        # Referral bonus message
        referral_bonus_msg = ""
        if referral_code:
            referral_bonus_msg = f"🎊 **BONUS!** You joined through a friend! Both of you get extra gifts! 🎁\n\n"

        # Main welcome content
        welcome_content = f"""
🎁 **WELCOME TO GIFTS BOT, {user_name.upper()}!** 🎁

{referral_bonus_msg}🌟 **EARN AMAZING GIFTS:**
• ✨ Daily Gift: ₹{Config.DAILY_BONUS_AMOUNT} every day!
• 👥 Share & Earn: ₹{Config.REFERRAL_REWARD} per friend!
• 🛍️ Claim Gifts: Canva Pro, Spotify, Netflix!

🚀 **HOW IT WORKS:**
Share your link → Friends join → You both get gifts!

🎯 **READY TO START?**
Tap the buttons below and start earning! 🎉
        """

        return welcome_content

    def _get_final_welcome_message(self, user, db_user, referral_code):
        """Generate final welcome message with branding"""
        user_name = db_user.get_display_name() if db_user else user.first_name

        # Create ASCII art logo
        logo = """
╔═══════════════════════════════════════╗
║  ██████╗ ██████╗  ██████╗             ║
║  ██╔══██╗██╔══██╗██╔═══██╗            ║
║  ██████╔╝██████╔╝██║   ██║            ║
║  ██╔═══╝ ██╔══██╗██║   ██║            ║
║  ██║     ██║  ██║╚██████╔╝            ║
║  ╚═╝     ╚═╝  ╚═╝ ╚═════╝             ║
║                                       ║
║     🎯 REFERRAL EARNING BOT 🎯        ║
╚═══════════════════════════════════════╝
        """

        # Referral bonus message
        referral_bonus_msg = ""
        if referral_code:
            referral_bonus_msg = f"""
🎊 **REFERRAL BONUS ACTIVATED!**
You joined through a referral link!
Both you and your referrer will earn rewards! 🎁
            """

        # Main welcome content
        welcome_content = f"""
{logo}

🌟 **Welcome, {user_name}!** 🌟

{referral_bonus_msg}

┌─────────────────────────────────────┐
│           💰 EARNING SYSTEM         │
├─────────────────────────────────────┤
│ 🎁 Daily Bonus:     ₹{Config.DAILY_BONUS_AMOUNT}           │
│ 👥 Per Referral:    ₹{Config.REFERRAL_REWARD}          │
│ 💸 Min Withdrawal:  ₹{Config.MINIMUM_WITHDRAWAL}         │
└─────────────────────────────────────┘

🚀 **GET STARTED:**
• Share your referral link with friends
• Claim daily bonuses every 24 hours
• Withdraw earnings as digital products
• Track your progress with analytics

💎 **PREMIUM FEATURES:**
✅ Real-time balance tracking
✅ Instant referral rewards
✅ Professional support
✅ Secure transactions
✅ Multiple withdrawal options

🎯 **Ready to start earning?**
Use the menu below to explore all features!
        """

        return welcome_content

    # ==================== ERROR HANDLER ====================

    async def error_handler(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle errors"""
        logger.error(f"Update {update} caused error {context.error}")

        # Try to send error message to user
        if update and update.effective_chat:
            try:
                await context.bot.send_message(
                    chat_id=update.effective_chat.id,
                    text="❌ An error occurred. Please try again later."
                )
            except:
                pass  # Ignore if we can't send the message

# ==================== MAIN EXECUTION ====================

def main():
    """Main function to run the complete bot"""
    try:
        logger.info("🤖 Starting Complete Telegram Referral Earning Bot...")
        logger.info("🔄 Mode: Long Polling (no webhook/domain required)")

        # Validate configuration
        Config.validate_config()
        logger.info("✅ Configuration validated")

        # Create bot instance
        bot = FinalBotApp()

        # Create application
        application = Application.builder().token(Config.BOT_TOKEN).build()
        bot.application = application

        # Add handlers
        application.add_handler(CommandHandler("start", bot.start_command))
        application.add_handler(CommandHandler("help", bot.help_command))
        application.add_handler(CommandHandler("balance", bot.balance_command))
        application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, bot.handle_message))
        application.add_handler(CallbackQueryHandler(bot.handle_callback))
        application.add_error_handler(bot.error_handler)

        logger.info("✅ All handlers added successfully")

        # Initialize async components in a separate task
        async def init_and_run():
            await bot.initialize_async_components()
            logger.info("🚀 Starting bot in long polling mode...")
            logger.info("✅ Bot is running! Press Ctrl+C to stop.")
            logger.info("ℹ️ No webhook, domain, or SSL configuration required.")
            logger.info(f"🤖 Bot username: @{Config.BOT_USERNAME}")

        # Run the initialization
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(init_and_run())

        # Run the bot
        application.run_polling(
            allowed_updates=Update.ALL_TYPES,
            drop_pending_updates=True
        )

    except KeyboardInterrupt:
        logger.info("👋 Bot stopped by user")
    except Exception as e:
        logger.error(f"❌ Bot error: {e}")
        raise

if __name__ == "__main__":
    main()
