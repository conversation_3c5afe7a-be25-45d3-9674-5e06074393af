"""
Admin command handlers
"""

import logging
from telegram import Update
from telegram.ext import ContextTypes

from src.utils.keyboards import Keyboards
from src.utils.security import SecurityValidator
from src.utils.logger import log_admin_action
from config import Config

logger = logging.getLogger(__name__)

class AdminHandler:
    """Handler for admin commands"""
    
    def __init__(self, bot):
        self.bot = bot
        self.admin_sessions = {}  # Store admin authentication sessions
    
    async def admin_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /admin command"""
        try:
            user_id = update.effective_user.id
            
            # Check if user is admin
            if user_id not in Config.ADMIN_USER_IDS:
                await update.message.reply_text("❌ You don't have admin privileges.")
                return
            
            # Check if already authenticated
            if user_id in self.admin_sessions:
                await self._send_admin_menu(update, context)
                return
            
            # Request password
            await update.message.reply_text(
                "🔐 **Admin Authentication**\n\nPlease enter the admin password:",
                parse_mode='Markdown'
            )
            
            # Set state for password input
            context.user_data['awaiting_admin_password'] = True
            
        except Exception as e:
            logger.error(f"Error in admin command: {e}")
            await update.message.reply_text("❌ An error occurred.")
    
    async def handle_admin_password(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle admin password input"""
        try:
            user_id = update.effective_user.id
            password = update.message.text
            
            # Delete the password message for security
            try:
                await update.message.delete()
            except:
                pass
            
            if SecurityValidator.verify_password(password, Config.ADMIN_PASSWORD):
                # Authentication successful
                self.admin_sessions[user_id] = True
                context.user_data.pop('awaiting_admin_password', None)
                
                await self._send_admin_menu(update, context)
                log_admin_action(user_id, "ADMIN_LOGIN_SUCCESS")
                
            else:
                await context.bot.send_message(
                    chat_id=user_id,
                    text="❌ Invalid password. Access denied."
                )
                context.user_data.pop('awaiting_admin_password', None)
                log_admin_action(user_id, "ADMIN_LOGIN_FAILED")
            
        except Exception as e:
            logger.error(f"Error handling admin password: {e}")
    
    async def _send_admin_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Send admin menu"""
        try:
            # Get bot statistics
            user_count = await self.bot.user_service.get_users_count()
            active_users = await self.bot.user_service.get_active_users_count()
            pending_withdrawals = len(await self.bot.withdrawal_service.get_pending_withdrawals())
            
            admin_text = f"""
👑 **Admin Panel**

**Bot Statistics:**
👥 Total Users: {user_count}
✅ Active Users: {active_users}
💸 Pending Withdrawals: {pending_withdrawals}

**Quick Actions:**
• Manage users and their accounts
• Process withdrawal requests
• Manage required channels
• Configure bot settings
• Send broadcast messages
• View detailed analytics

Choose an option from the menu below:
            """
            
            if update.callback_query:
                await update.callback_query.edit_message_text(
                    admin_text,
                    parse_mode='Markdown',
                    reply_markup=None
                )
                await context.bot.send_message(
                    chat_id=update.effective_user.id,
                    text="Select an admin option:",
                    reply_markup=Keyboards.admin_menu()
                )
            else:
                await context.bot.send_message(
                    chat_id=update.effective_user.id,
                    text=admin_text,
                    parse_mode='Markdown',
                    reply_markup=Keyboards.admin_menu()
                )
            
        except Exception as e:
            logger.error(f"Error sending admin menu: {e}")
    
    async def handle_admin_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle admin menu messages"""
        try:
            user_id = update.effective_user.id
            message_text = update.message.text
            
            # Check admin authentication
            if user_id not in self.admin_sessions:
                await update.message.reply_text("❌ Please authenticate first with /admin")
                return
            
            # Handle admin menu options
            if message_text == "👥 Users":
                await self._handle_users_menu(update, context)
            elif message_text == "💸 Withdrawals":
                await self._handle_withdrawals_menu(update, context)
            elif message_text == "📢 Channels":
                await self._handle_channels_menu(update, context)
            elif message_text == "🛍️ Products":
                await self._handle_products_menu(update, context)
            elif message_text == "📊 Analytics":
                await self._handle_analytics_menu(update, context)
            elif message_text == "⚙️ Settings":
                await self._handle_settings_menu(update, context)
            elif message_text == "📨 Broadcast":
                await self._handle_broadcast_menu(update, context)
            elif message_text == "🔙 Back to User":
                await self._logout_admin(update, context)
            else:
                await update.message.reply_text(
                    "Please use the admin menu buttons.",
                    reply_markup=Keyboards.admin_menu()
                )
            
        except Exception as e:
            logger.error(f"Error handling admin message: {e}")
    
    async def _handle_users_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle users management menu"""
        try:
            users_text = """
👥 **User Management**

**Available Actions:**
• View all users
• Search users
• Ban/unban users
• Modify user balances
• View user statistics

Type a user ID or username to search, or use the commands:
/users_list - View all users
/user_search <query> - Search users
/user_stats <user_id> - View user stats
            """
            
            await update.message.reply_text(users_text, parse_mode='Markdown')
            
        except Exception as e:
            logger.error(f"Error in users menu: {e}")
    
    async def _handle_withdrawals_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle withdrawals management menu"""
        try:
            pending_withdrawals = await self.bot.withdrawal_service.get_pending_withdrawals(10)
            
            if not pending_withdrawals:
                await update.message.reply_text("✅ No pending withdrawals.")
                return
            
            withdrawals_text = "💸 **Pending Withdrawals**\n\n"
            
            for i, withdrawal in enumerate(pending_withdrawals[:5], 1):
                user = await self.bot.user_service.get_user(withdrawal.user_id)
                user_name = user.get_display_name() if user else f"User {withdrawal.user_id}"
                
                withdrawals_text += f"{i}. **{user_name}**\n"
                withdrawals_text += f"   Amount: ₹{withdrawal.amount:.2f}\n"
                withdrawals_text += f"   Type: {withdrawal.withdrawal_type.value}\n"
                withdrawals_text += f"   Date: {withdrawal.created_at.strftime('%d/%m/%Y %H:%M')}\n\n"
            
            if len(pending_withdrawals) > 5:
                withdrawals_text += f"... and {len(pending_withdrawals) - 5} more\n\n"
            
            withdrawals_text += "Use /withdrawal <withdrawal_id> to manage specific withdrawals."
            
            await update.message.reply_text(withdrawals_text, parse_mode='Markdown')
            
        except Exception as e:
            logger.error(f"Error in withdrawals menu: {e}")
    
    async def _handle_channels_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle channels management menu"""
        try:
            channels = await self.bot.channel_service.get_all_channels()
            
            channels_text = "📢 **Channel Management**\n\n"
            
            if not channels:
                channels_text += "No channels configured.\n\n"
            else:
                for i, channel in enumerate(channels[:5], 1):
                    status = "🟢" if channel.is_active else "🔴"
                    channels_text += f"{i}. {status} **{channel.channel_name}**\n"
                    channels_text += f"   ID: `{channel.channel_id}`\n"
                    channels_text += f"   Required: {'Yes' if channel.is_required else 'No'}\n\n"
            
            channels_text += """
**Commands:**
/add_channel - Add new channel
/channel_list - View all channels
/channel <channel_id> - Manage specific channel
            """
            
            await update.message.reply_text(channels_text, parse_mode='Markdown')
            
        except Exception as e:
            logger.error(f"Error in channels menu: {e}")
    
    async def _handle_products_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle products management menu"""
        try:
            products = await self.bot.product_service.get_all_products(include_inactive=True)
            
            products_text = "🛍️ **Product Management**\n\n"
            
            if not products:
                products_text += "No products configured.\n\n"
            else:
                for i, product in enumerate(products[:5], 1):
                    status = "🟢" if product.is_active else "🔴"
                    featured = "⭐" if product.is_featured else ""
                    products_text += f"{i}. {status}{featured} **{product.name}**\n"
                    products_text += f"   Price: ₹{product.price:.2f}\n"
                    products_text += f"   Category: {product.category}\n\n"
            
            products_text += """
**Commands:**
/add_product - Add new product
/product_list - View all products
/product <product_id> - Manage specific product
            """
            
            await update.message.reply_text(products_text, parse_mode='Markdown')
            
        except Exception as e:
            logger.error(f"Error in products menu: {e}")
    
    async def _handle_analytics_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle analytics menu"""
        try:
            # Get basic statistics
            user_stats = await self.bot.user_service.get_users_count()
            
            analytics_text = f"""
📊 **Bot Analytics**

**User Statistics:**
👥 Total Users: {user_stats}

**Available Reports:**
• User growth trends
• Referral performance
• Withdrawal statistics
• Channel subscription rates
• Product popularity

Use /analytics <report_type> for detailed reports.
            """
            
            await update.message.reply_text(analytics_text, parse_mode='Markdown')
            
        except Exception as e:
            logger.error(f"Error in analytics menu: {e}")
    
    async def _handle_settings_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle settings menu"""
        try:
            settings_text = f"""
⚙️ **Bot Settings**

**Current Configuration:**
💰 Referral Reward: ₹{Config.REFERRAL_REWARD}
🎁 Daily Bonus: ₹{Config.DAILY_BONUS_AMOUNT}
💸 Minimum Withdrawal: ₹{Config.MINIMUM_WITHDRAWAL}

**Available Settings:**
• Modify reward amounts
• Update bot messages
• Configure rate limits
• Manage bot status

Use /setting <key> <value> to update settings.
            """
            
            await update.message.reply_text(settings_text, parse_mode='Markdown')
            
        except Exception as e:
            logger.error(f"Error in settings menu: {e}")
    
    async def _handle_broadcast_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle broadcast menu"""
        try:
            broadcast_text = """
📨 **Broadcast Message**

Send a message to all users or specific groups.

**Commands:**
/broadcast <message> - Send to all users
/broadcast_active <message> - Send to active users only
/broadcast_test <message> - Send test to admins only

**Tips:**
• Use Markdown formatting
• Keep messages concise
• Test before sending to all users
            """
            
            await update.message.reply_text(broadcast_text, parse_mode='Markdown')
            
        except Exception as e:
            logger.error(f"Error in broadcast menu: {e}")
    
    async def _logout_admin(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Logout admin and return to user mode"""
        try:
            user_id = update.effective_user.id
            
            if user_id in self.admin_sessions:
                del self.admin_sessions[user_id]
            
            await update.message.reply_text(
                "👋 Logged out from admin panel.",
                reply_markup=Keyboards.main_menu()
            )
            
            log_admin_action(user_id, "ADMIN_LOGOUT")
            
        except Exception as e:
            logger.error(f"Error logging out admin: {e}")
    
    def is_admin_authenticated(self, user_id: int) -> bool:
        """Check if user is authenticated admin"""
        return user_id in Config.ADMIN_USER_IDS and user_id in self.admin_sessions
