"""
Start and help command handlers
"""

import logging
from telegram import Update
from telegram.ext import ContextTypes

from src.utils.keyboards import Keyboards
from src.utils.helpers import MessageFormatter, TextUtils
from src.utils.logger import log_user_action
from config import Config

logger = logging.getLogger(__name__)

class StartHandler:
    """Handler for start and help commands"""
    
    def __init__(self, bot):
        self.bot = bot
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command"""
        try:
            user = update.effective_user
            chat = update.effective_chat
            
            # Check user permissions
            permissions = await self.bot.check_user_permissions(user.id)
            if not permissions['allowed']:
                if permissions['channels_required']:
                    await self._send_channel_join_message(update, context, permissions['missing_channels'])
                    return
                else:
                    await update.message.reply_text(permissions['reason'])
                    return
            
            # Extract referral code from command
            referral_code = None
            if context.args:
                referral_code = TextUtils.extract_referral_code(f"/start {context.args[0]}")
            
            # Create or get user
            user_data = {
                'user_id': user.id,
                'username': user.username,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'language_code': user.language_code,
                'is_bot': user.is_bot,
                'is_premium': getattr(user, 'is_premium', False)
            }
            
            db_user = await self.bot.user_service.create_user(user_data, referral_code)
            
            if not db_user:
                await update.message.reply_text("❌ Failed to register. Please try again.")
                return
            
            # Process referral if applicable
            if referral_code and db_user.referred_by:
                await self._process_referral(db_user.user_id, db_user.referred_by, referral_code)
            
            # Send welcome message
            welcome_text = Config.WELCOME_MESSAGE.format(
                name=db_user.get_display_name(),
                currency=Config.CURRENCY_SYMBOL,
                referral_reward=Config.REFERRAL_REWARD,
                min_withdrawal=Config.MINIMUM_WITHDRAWAL
            )
            
            await update.message.reply_text(
                welcome_text,
                reply_markup=Keyboards.main_menu(),
                parse_mode='Markdown'
            )
            
            log_user_action(user.id, "START_COMMAND", f"Referral: {referral_code}")
            
        except Exception as e:
            logger.error(f"Error in start command: {e}")
            await update.message.reply_text("❌ An error occurred. Please try again.")
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        try:
            user_id = update.effective_user.id
            
            # Check user permissions
            permissions = await self.bot.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return
            
            help_text = f"""
🤖 **Referral Earning Bot Help**

**Main Features:**
💰 **Balance** - Check your current balance and earnings
🎁 **Daily Bonus** - Claim your daily bonus (₹{Config.DAILY_BONUS_AMOUNT})
💸 **Withdraw** - Withdraw your earnings (Min: ₹{Config.MINIMUM_WITHDRAWAL})
👥 **Referrals** - Share your referral link and earn ₹{Config.REFERRAL_REWARD} per referral
📊 **Statistics** - View your account statistics
⚙️ **Settings** - Manage your account settings

**How to Earn:**
1. **Referrals**: Share your referral link with friends
2. **Daily Bonus**: Claim daily bonus every 24 hours
3. **Activities**: Participate in bot activities

**Withdrawal:**
- Minimum withdrawal: ₹{Config.MINIMUM_WITHDRAWAL}
- Available products: Canva Pro, Spotify Premium, Netflix, etc.
- Processing time: 24-48 hours

**Commands:**
/start - Start the bot
/help - Show this help message
/balance - Check your balance
/referrals - View referral information
/withdraw - Start withdrawal process
/stats - View your statistics

**Support:**
If you need help, contact the administrators.

Happy earning! 🚀
            """
            
            await update.message.reply_text(
                help_text,
                parse_mode='Markdown',
                reply_markup=Keyboards.main_menu()
            )
            
            log_user_action(user_id, "HELP_COMMAND")
            
        except Exception as e:
            logger.error(f"Error in help command: {e}")
            await update.message.reply_text("❌ An error occurred. Please try again.")
    
    async def _send_channel_join_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE, missing_channels):
        """Send message asking user to join required channels"""
        try:
            channels_text = "📢 **Please join the following channels to use this bot:**\n\n"
            
            for i, channel in enumerate(missing_channels, 1):
                channels_text += f"{i}. {channel['channel_name']}\n"
            
            channels_text += "\n✅ After joining all channels, click the button below to verify."
            
            keyboard = Keyboards.channel_join_keyboard(missing_channels)
            
            await update.message.reply_text(
                channels_text,
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            
        except Exception as e:
            logger.error(f"Error sending channel join message: {e}")
    
    async def _process_referral(self, referred_user_id: int, referrer_user_id: int, referral_code: str):
        """Process a new referral"""
        try:
            # Create referral record
            referral = await self.bot.referral_service.create_referral(
                referrer_user_id,
                referred_user_id,
                referral_code
            )
            
            if referral:
                # Complete the referral and give reward
                success = await self.bot.referral_service.complete_referral(referral.referral_id)
                
                if success:
                    # Notify referrer
                    try:
                        referrer = await self.bot.user_service.get_user(referrer_user_id)
                        if referrer:
                            notification_text = f"""
🎉 **New Referral!**

You earned ₹{Config.REFERRAL_REWARD} for referring a new user!

💰 Your new balance: ₹{referrer.balance + Config.REFERRAL_REWARD:.2f}
👥 Total referrals: {referrer.referral_count + 1}

Keep sharing your referral link to earn more! 🚀
                            """
                            
                            await self.bot.application.bot.send_message(
                                chat_id=referrer_user_id,
                                text=notification_text,
                                parse_mode='Markdown'
                            )
                    except Exception as e:
                        logger.error(f"Failed to notify referrer {referrer_user_id}: {e}")
                
                logger.info(f"Processed referral: {referrer_user_id} -> {referred_user_id}")
            
        except Exception as e:
            logger.error(f"Error processing referral: {e}")
    
    async def verify_channels_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle channel verification callback"""
        try:
            query = update.callback_query
            user_id = query.from_user.id
            
            await query.answer()
            
            # Verify user subscriptions
            verification_result = await self.bot.channel_service.verify_user_subscriptions(user_id)
            
            if verification_result['all_joined']:
                # Update user channel status
                await self.bot.user_service.update_user_channel_status(user_id, "all", True)
                
                # Send welcome message
                welcome_text = f"""
✅ **Verification Successful!**

Welcome to the Referral Earning Bot! 🎉

{Config.WELCOME_MESSAGE}
                """
                
                await query.edit_message_text(
                    welcome_text,
                    parse_mode='Markdown',
                    reply_markup=None
                )
                
                # Send main menu
                await context.bot.send_message(
                    chat_id=user_id,
                    text="Choose an option from the menu below:",
                    reply_markup=Keyboards.main_menu()
                )
                
                log_user_action(user_id, "CHANNELS_VERIFIED")
                
            else:
                missing_channels = verification_result['missing_channels']
                await query.edit_message_text(
                    f"❌ You haven't joined all required channels yet.\n\n"
                    f"Missing channels: {len(missing_channels)}\n"
                    f"Please join all channels and try again.",
                    reply_markup=Keyboards.channel_join_keyboard(missing_channels)
                )
            
        except Exception as e:
            logger.error(f"Error in channel verification: {e}")
            await query.answer("❌ Verification failed. Please try again.")
    
    async def send_bot_info(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Send bot information"""
        try:
            bot_info = await self.bot.get_bot_info()
            
            info_text = f"""
🤖 **Bot Information**

**Name:** {bot_info.get('first_name', 'Referral Bot')}
**Username:** @{bot_info.get('username', 'N/A')}
**ID:** `{bot_info.get('id', 'N/A')}`

**Features:**
✅ Referral System
✅ Daily Bonuses
✅ Digital Product Withdrawals
✅ Channel Subscription Management
✅ Admin Panel
✅ Analytics & Statistics

**Currency:** Indian Rupees (₹)
**Referral Reward:** ₹{Config.REFERRAL_REWARD}
**Daily Bonus:** ₹{Config.DAILY_BONUS_AMOUNT}
**Minimum Withdrawal:** ₹{Config.MINIMUM_WITHDRAWAL}

**Status:** 🟢 Online and Active
            """
            
            await update.message.reply_text(
                info_text,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            logger.error(f"Error sending bot info: {e}")
            await update.message.reply_text("❌ Failed to get bot information.")
