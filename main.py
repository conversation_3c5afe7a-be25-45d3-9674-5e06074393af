#!/usr/bin/env python3
"""
Telegram Referral Earning Bot
Main entry point for the bot application
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import Config
from src.bot import ReferralBot
from src.database import Database
from src.utils.logger import setup_logger

async def main():
    """Main function to start the bot"""
    try:
        # Validate configuration
        Config.validate_config()
        
        # Setup logging
        logger = setup_logger()
        logger.info("Starting Telegram Referral Earning Bot...")
        logger.info("Mode: Long Polling (no webhook/domain required)")

        # Initialize database
        database = Database()
        await database.connect()
        logger.info("Database connected successfully")

        # Initialize bot
        bot = ReferralBot(database)

        # Setup bot handlers
        await bot._add_handlers()
        await bot._initialize_bot_data()

        # Start polling (this will block)
        bot.start_polling()
        
    except Exception as e:
        logging.error(f"Failed to start bot: {e}")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logging.info("Bot stopped by user")
    except Exception as e:
        logging.error(f"Unexpected error: {e}")
        sys.exit(1)
