"""
Product model for the Telegram Referral Bot
"""

from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
from dataclasses import dataclass, asdict
import uuid

@dataclass
class Product:
    """Product model representing digital products for withdrawal"""
    
    name: str
    price: float
    description: str
    category: str
    
    # Product settings
    is_active: bool = True
    is_featured: bool = False
    stock_quantity: Optional[int] = None  # None = unlimited
    
    # Optional fields
    product_id: Optional[str] = None
    image_url: Optional[str] = None
    terms_conditions: Optional[str] = None
    delivery_instructions: Optional[str] = None
    
    # Admin fields
    added_by: Optional[int] = None  # Admin user ID who added the product
    
    # Timestamps
    created_at: datetime = None
    updated_at: datetime = None
    
    # Statistics
    total_orders: int = 0
    successful_deliveries: int = 0
    pending_orders: int = 0
    
    # Metadata
    metadata: Optional[Dict[str, Any]] = None
    tags: Optional[List[str]] = None
    
    def __post_init__(self):
        """Initialize default values after object creation"""
        if self.product_id is None:
            self.product_id = str(uuid.uuid4())
        
        if self.created_at is None:
            self.created_at = datetime.now(timezone.utc)
        
        if self.updated_at is None:
            self.updated_at = datetime.now(timezone.utc)
        
        if self.metadata is None:
            self.metadata = {}
        
        if self.tags is None:
            self.tags = []
    
    def is_available(self) -> bool:
        """Check if product is available for purchase"""
        if not self.is_active:
            return False
        
        if self.stock_quantity is not None:
            return self.stock_quantity > 0
        
        return True
    
    def is_in_stock(self) -> bool:
        """Check if product is in stock"""
        if self.stock_quantity is None:
            return True  # Unlimited stock
        
        return self.stock_quantity > 0
    
    def reduce_stock(self, quantity: int = 1) -> bool:
        """Reduce stock quantity"""
        if self.stock_quantity is None:
            return True  # Unlimited stock
        
        if self.stock_quantity >= quantity:
            self.stock_quantity -= quantity
            self.updated_at = datetime.now(timezone.utc)
            return True
        
        return False
    
    def increase_stock(self, quantity: int = 1):
        """Increase stock quantity"""
        if self.stock_quantity is not None:
            self.stock_quantity += quantity
            self.updated_at = datetime.now(timezone.utc)
    
    def add_order(self):
        """Increment order count"""
        self.total_orders += 1
        self.pending_orders += 1
        self.updated_at = datetime.now(timezone.utc)
    
    def complete_order(self):
        """Mark order as completed"""
        if self.pending_orders > 0:
            self.pending_orders -= 1
        
        self.successful_deliveries += 1
        self.updated_at = datetime.now(timezone.utc)
    
    def cancel_order(self):
        """Cancel an order"""
        if self.pending_orders > 0:
            self.pending_orders -= 1
        
        self.updated_at = datetime.now(timezone.utc)
    
    def activate_product(self):
        """Activate the product"""
        self.is_active = True
        self.updated_at = datetime.now(timezone.utc)
    
    def deactivate_product(self):
        """Deactivate the product"""
        self.is_active = False
        self.updated_at = datetime.now(timezone.utc)
    
    def set_featured(self, featured: bool = True):
        """Set product as featured"""
        self.is_featured = featured
        self.updated_at = datetime.now(timezone.utc)
    
    def add_tag(self, tag: str):
        """Add tag to product"""
        if tag not in self.tags:
            self.tags.append(tag)
            self.updated_at = datetime.now(timezone.utc)
    
    def remove_tag(self, tag: str):
        """Remove tag from product"""
        if tag in self.tags:
            self.tags.remove(tag)
            self.updated_at = datetime.now(timezone.utc)
    
    def add_metadata(self, key: str, value: Any):
        """Add metadata to product"""
        self.metadata[key] = value
        self.updated_at = datetime.now(timezone.utc)
    
    def get_formatted_price(self, currency_symbol: str = "₹") -> str:
        """Get formatted price with currency symbol"""
        return f"{currency_symbol}{self.price:.2f}"
    
    def get_stock_display(self) -> str:
        """Get stock display string"""
        if self.stock_quantity is None:
            return "♾️ Unlimited"
        elif self.stock_quantity == 0:
            return "❌ Out of Stock"
        else:
            return f"📦 {self.stock_quantity} available"
    
    def get_status_display(self) -> str:
        """Get product status display"""
        if not self.is_active:
            return "🔴 Inactive"
        elif not self.is_in_stock():
            return "⚠️ Out of Stock"
        elif self.is_featured:
            return "⭐ Featured"
        else:
            return "🟢 Active"
    
    def get_popularity_score(self) -> float:
        """Calculate popularity score based on orders and deliveries"""
        if self.total_orders == 0:
            return 0.0
        
        success_rate = self.successful_deliveries / self.total_orders
        return (self.total_orders * 0.7) + (success_rate * 30)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert product object to dictionary"""
        data = asdict(self)
        
        # Convert datetime objects to ISO format
        datetime_fields = ['created_at', 'updated_at']
        for field in datetime_fields:
            if field in data and data[field]:
                data[field] = data[field].isoformat()
        
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Product':
        """Create product object from dictionary"""
        # Create a copy of data to avoid modifying the original
        product_data = data.copy()

        # Remove MongoDB's _id field if present
        product_data.pop('_id', None)

        # Convert ISO format strings back to datetime objects
        datetime_fields = ['created_at', 'updated_at']
        for field in datetime_fields:
            if field in product_data and product_data[field]:
                if isinstance(product_data[field], str):
                    product_data[field] = datetime.fromisoformat(product_data[field].replace('Z', '+00:00'))

        return cls(**product_data)
    
    def __str__(self) -> str:
        """String representation of product"""
        return f"Product(id={self.product_id}, name={self.name}, price={self.price}, category={self.category})"
