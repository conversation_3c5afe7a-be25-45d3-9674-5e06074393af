"""
Withdrawal service for managing withdrawal operations
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Optional, List, Dict, Any

from src.database import Database
from src.models.withdrawal import Withdrawal, WithdrawalStatus, WithdrawalType
from src.models.transaction import Transaction, TransactionType, TransactionStatus
from src.utils.logger import log_user_action, log_admin_action
from config import Config

logger = logging.getLogger(__name__)

class WithdrawalService:
    """Service for withdrawal management operations"""
    
    def __init__(self, database: Database):
        self.db = database
    
    async def create_withdrawal_request(self, user_id: int, amount: float, withdrawal_type: WithdrawalType,
                                      product_id: str = None, payment_details: Dict = None) -> Optional[Withdrawal]:
        """Create a new withdrawal request"""
        try:
            # Check user balance
            user_data = await self.db.users.find_one({'user_id': user_id})
            if not user_data or user_data['balance'] < amount:
                return None
            
            # Check minimum withdrawal amount
            if amount < Config.MINIMUM_WITHDRAWAL:
                return None
            
            # Get product details if product withdrawal
            product_name = None
            if withdrawal_type == WithdrawalType.PRODUCT and product_id:
                product_data = await self.db.products.find_one({'product_id': product_id})
                if product_data:
                    product_name = product_data['name']
            
            # Create withdrawal
            withdrawal = Withdrawal(
                user_id=user_id,
                amount=amount,
                withdrawal_type=withdrawal_type,
                product_id=product_id,
                product_name=product_name,
                payment_details=payment_details or {}
            )
            
            # Create corresponding transaction
            transaction = Transaction(
                user_id=user_id,
                amount=-amount,  # Negative for withdrawal
                transaction_type=TransactionType.WITHDRAWAL,
                status=TransactionStatus.PENDING,
                description=f"Withdrawal request - {withdrawal_type.value}",
                reference_id=withdrawal.withdrawal_id
            )
            
            # Save withdrawal and transaction
            await self.db.withdrawals.insert_one(withdrawal.to_dict())
            await self.db.transactions.insert_one(transaction.to_dict())
            
            # Deduct balance (hold the amount)
            await self.db.users.update_one(
                {'user_id': user_id},
                {
                    '$inc': {'balance': -amount},
                    '$set': {'updated_at': datetime.now(timezone.utc).isoformat()}
                }
            )
            
            withdrawal.transaction_id = transaction.transaction_id
            await self.update_withdrawal(withdrawal)
            
            log_user_action(user_id, "WITHDRAWAL_REQUESTED", f"Amount: {amount}, Type: {withdrawal_type.value}")
            logger.info(f"Created withdrawal request {withdrawal.withdrawal_id} for user {user_id}")
            
            return withdrawal
            
        except Exception as e:
            logger.error(f"Failed to create withdrawal request for user {user_id}: {e}")
            return None
    
    async def get_withdrawal(self, withdrawal_id: str) -> Optional[Withdrawal]:
        """Get withdrawal by ID"""
        try:
            withdrawal_data = await self.db.withdrawals.find_one({'withdrawal_id': withdrawal_id})
            if withdrawal_data:
                return Withdrawal.from_dict(withdrawal_data)
            return None
        except Exception as e:
            logger.error(f"Failed to get withdrawal {withdrawal_id}: {e}")
            return None
    
    async def update_withdrawal(self, withdrawal: Withdrawal) -> bool:
        """Update withdrawal in database"""
        try:
            withdrawal.updated_at = datetime.now(timezone.utc)
            result = await self.db.withdrawals.update_one(
                {'withdrawal_id': withdrawal.withdrawal_id},
                {'$set': withdrawal.to_dict()}
            )
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Failed to update withdrawal {withdrawal.withdrawal_id}: {e}")
            return False
    
    async def approve_withdrawal(self, withdrawal_id: str, admin_id: int, notes: str = "") -> bool:
        """Approve a withdrawal request"""
        try:
            withdrawal = await self.get_withdrawal(withdrawal_id)
            if not withdrawal or not withdrawal.is_pending():
                return False
            
            withdrawal.approve_withdrawal(admin_id, notes)
            result = await self.update_withdrawal(withdrawal)
            
            if result:
                # Update transaction status
                if withdrawal.transaction_id:
                    await self.db.transactions.update_one(
                        {'transaction_id': withdrawal.transaction_id},
                        {'$set': {
                            'status': TransactionStatus.COMPLETED.value,
                            'processed_by': admin_id,
                            'admin_notes': notes,
                            'completed_at': datetime.now(timezone.utc).isoformat()
                        }}
                    )
                
                log_admin_action(admin_id, "WITHDRAWAL_APPROVED", withdrawal_id, f"Amount: {withdrawal.amount}")
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to approve withdrawal {withdrawal_id}: {e}")
            return False
    
    async def reject_withdrawal(self, withdrawal_id: str, admin_id: int, reason: str) -> bool:
        """Reject a withdrawal request"""
        try:
            withdrawal = await self.get_withdrawal(withdrawal_id)
            if not withdrawal or not withdrawal.is_pending():
                return False
            
            withdrawal.reject_withdrawal(admin_id, reason)
            result = await self.update_withdrawal(withdrawal)
            
            if result:
                # Refund the amount to user
                await self.db.users.update_one(
                    {'user_id': withdrawal.user_id},
                    {
                        '$inc': {'balance': withdrawal.amount},
                        '$set': {'updated_at': datetime.now(timezone.utc).isoformat()}
                    }
                )
                
                # Update transaction status
                if withdrawal.transaction_id:
                    await self.db.transactions.update_one(
                        {'transaction_id': withdrawal.transaction_id},
                        {'$set': {
                            'status': TransactionStatus.FAILED.value,
                            'processed_by': admin_id,
                            'admin_notes': reason,
                            'updated_at': datetime.now(timezone.utc).isoformat()
                        }}
                    )
                
                log_admin_action(admin_id, "WITHDRAWAL_REJECTED", withdrawal_id, f"Reason: {reason}")
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to reject withdrawal {withdrawal_id}: {e}")
            return False
    
    async def complete_withdrawal(self, withdrawal_id: str, admin_id: int, delivery_info: str = "") -> bool:
        """Mark withdrawal as completed"""
        try:
            withdrawal = await self.get_withdrawal(withdrawal_id)
            if not withdrawal or not withdrawal.is_approved():
                return False
            
            withdrawal.complete_withdrawal(admin_id, delivery_info)
            result = await self.update_withdrawal(withdrawal)
            
            if result:
                # Update user withdrawal statistics
                await self.db.users.update_one(
                    {'user_id': withdrawal.user_id},
                    {
                        '$inc': {
                            'total_withdrawals': withdrawal.amount,
                            'withdrawal_count': 1
                        },
                        '$set': {'updated_at': datetime.now(timezone.utc).isoformat()}
                    }
                )
                
                log_admin_action(admin_id, "WITHDRAWAL_COMPLETED", withdrawal_id, f"Delivery: {delivery_info}")
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to complete withdrawal {withdrawal_id}: {e}")
            return False
    
    async def get_user_withdrawals(self, user_id: int, limit: int = 50) -> List[Withdrawal]:
        """Get withdrawals for a user"""
        try:
            cursor = self.db.withdrawals.find({'user_id': user_id}).sort('created_at', -1).limit(limit)
            withdrawals = []
            
            async for withdrawal_data in cursor:
                withdrawals.append(Withdrawal.from_dict(withdrawal_data))
            
            return withdrawals
            
        except Exception as e:
            logger.error(f"Failed to get withdrawals for user {user_id}: {e}")
            return []
    
    async def get_pending_withdrawals(self, limit: int = 100) -> List[Withdrawal]:
        """Get pending withdrawal requests"""
        try:
            cursor = self.db.withdrawals.find({
                'status': WithdrawalStatus.PENDING.value
            }).sort('created_at', 1).limit(limit)
            
            withdrawals = []
            async for withdrawal_data in cursor:
                withdrawals.append(Withdrawal.from_dict(withdrawal_data))
            
            return withdrawals
            
        except Exception as e:
            logger.error(f"Failed to get pending withdrawals: {e}")
            return []
    
    async def get_withdrawal_statistics(self, days: int = 30) -> Dict[str, Any]:
        """Get withdrawal statistics"""
        try:
            start_date = datetime.now(timezone.utc) - timedelta(days=days)
            
            # Total withdrawals
            total_withdrawals = await self.db.withdrawals.count_documents({
                'created_at': {'$gte': start_date.isoformat()}
            })
            
            # Completed withdrawals
            completed_withdrawals = await self.db.withdrawals.count_documents({
                'created_at': {'$gte': start_date.isoformat()},
                'status': WithdrawalStatus.COMPLETED.value
            })
            
            # Pending withdrawals
            pending_withdrawals = await self.db.withdrawals.count_documents({
                'status': WithdrawalStatus.PENDING.value
            })
            
            # Total amount
            pipeline = [
                {'$match': {
                    'created_at': {'$gte': start_date.isoformat()},
                    'status': WithdrawalStatus.COMPLETED.value
                }},
                {'$group': {
                    '_id': None,
                    'total_amount': {'$sum': '$amount'},
                    'avg_amount': {'$avg': '$amount'}
                }}
            ]
            
            result = await self.db.withdrawals.aggregate(pipeline).to_list(1)
            total_amount = result[0]['total_amount'] if result else 0.0
            avg_amount = result[0]['avg_amount'] if result else 0.0
            
            # Withdrawal types breakdown
            type_pipeline = [
                {'$match': {
                    'created_at': {'$gte': start_date.isoformat()},
                    'status': WithdrawalStatus.COMPLETED.value
                }},
                {'$group': {
                    '_id': '$withdrawal_type',
                    'count': {'$sum': 1},
                    'total_amount': {'$sum': '$amount'}
                }}
            ]
            
            type_breakdown = {}
            async for entry in self.db.withdrawals.aggregate(type_pipeline):
                type_breakdown[entry['_id']] = {
                    'count': entry['count'],
                    'total_amount': entry['total_amount']
                }
            
            return {
                'period_days': days,
                'total_withdrawals': total_withdrawals,
                'completed_withdrawals': completed_withdrawals,
                'pending_withdrawals': pending_withdrawals,
                'completion_rate': (completed_withdrawals / total_withdrawals * 100) if total_withdrawals > 0 else 0,
                'total_amount': total_amount,
                'average_amount': avg_amount,
                'type_breakdown': type_breakdown
            }
            
        except Exception as e:
            logger.error(f"Failed to get withdrawal statistics: {e}")
            return {}
    
    async def can_user_withdraw(self, user_id: int) -> Dict[str, Any]:
        """Check if user can make a withdrawal"""
        try:
            user_data = await self.db.users.find_one({'user_id': user_id})
            if not user_data:
                return {'can_withdraw': False, 'reason': 'User not found'}
            
            if user_data['is_banned']:
                return {'can_withdraw': False, 'reason': 'User is banned'}
            
            if user_data['balance'] < Config.MINIMUM_WITHDRAWAL:
                return {
                    'can_withdraw': False,
                    'reason': f'Minimum withdrawal amount is ₹{Config.MINIMUM_WITHDRAWAL}',
                    'needed_amount': Config.MINIMUM_WITHDRAWAL - user_data['balance']
                }
            
            # Check for recent withdrawals (optional cooldown)
            recent_withdrawal = await self.db.withdrawals.find_one({
                'user_id': user_id,
                'created_at': {'$gte': (datetime.now(timezone.utc) - timedelta(hours=Config.WITHDRAWAL_COOLDOWN_HOURS)).isoformat()}
            })
            
            if recent_withdrawal:
                return {
                    'can_withdraw': False,
                    'reason': f'Please wait {Config.WITHDRAWAL_COOLDOWN_HOURS} hours between withdrawals'
                }
            
            return {'can_withdraw': True, 'max_amount': user_data['balance']}
            
        except Exception as e:
            logger.error(f"Failed to check withdrawal eligibility for user {user_id}: {e}")
            return {'can_withdraw': False, 'reason': 'System error'}
