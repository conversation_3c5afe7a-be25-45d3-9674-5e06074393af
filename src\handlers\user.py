"""
User command handlers
"""

import logging
from datetime import datetime, timezone
from telegram import Update
from telegram.ext import ContextTypes

from src.utils.keyboards import Keyboards
from src.utils.helpers import MessageFormatter, QRCodeGenerator
from src.utils.logger import log_user_action
from config import Config

logger = logging.getLogger(__name__)

class UserHandler:
    """Handler for user commands and messages"""
    
    def __init__(self, bot):
        self.bot = bot
    
    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle text messages"""
        try:
            user_id = update.effective_user.id
            message_text = update.message.text
            
            # Check user permissions
            permissions = await self.bot.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return
            
            # Handle menu buttons
            if message_text == "💰 Balance":
                await self.balance_command(update, context)
            elif message_text == "🎁 Daily Bonus":
                await self.daily_bonus_command(update, context)
            elif message_text == "💸 Withdraw":
                await self.withdraw_command(update, context)
            elif message_text == "👥 Referrals":
                await self.referrals_command(update, context)
            elif message_text == "📊 Statistics":
                await self.stats_command(update, context)
            elif message_text == "⚙️ Settings":
                await self.settings_command(update, context)
            else:
                await update.message.reply_text(
                    "Please use the menu buttons below or type /help for assistance.",
                    reply_markup=Keyboards.main_menu()
                )
            
        except Exception as e:
            logger.error(f"Error handling message: {e}")
            await update.message.reply_text("❌ An error occurred. Please try again.")
    
    async def balance_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle balance command"""
        try:
            user_id = update.effective_user.id
            
            user = await self.bot.user_service.get_user(user_id)
            if not user:
                await update.message.reply_text("❌ User not found. Please start the bot with /start")
                return
            
            balance_text = MessageFormatter.format_balance(user.balance, user.total_earned)
            
            await update.message.reply_text(
                balance_text,
                parse_mode='Markdown',
                reply_markup=Keyboards.balance_menu()
            )
            
            log_user_action(user_id, "BALANCE_CHECKED")
            
        except Exception as e:
            logger.error(f"Error in balance command: {e}")
            await update.message.reply_text("❌ Failed to get balance information.")
    
    async def daily_bonus_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle daily bonus command"""
        try:
            user_id = update.effective_user.id
            
            user = await self.bot.user_service.get_user(user_id)
            if not user:
                await update.message.reply_text("❌ User not found. Please start the bot with /start")
                return
            
            if not user.can_claim_daily_bonus():
                # Calculate time remaining
                if user.last_daily_bonus:
                    next_claim = user.last_daily_bonus.replace(tzinfo=timezone.utc) + \
                               datetime.timedelta(hours=24)
                    remaining = next_claim - datetime.now(timezone.utc)
                    hours = int(remaining.total_seconds() // 3600)
                    minutes = int((remaining.total_seconds() % 3600) // 60)
                    
                    await update.message.reply_text(
                        f"⏰ You can claim your next daily bonus in {hours}h {minutes}m"
                    )
                else:
                    await update.message.reply_text("⏰ Daily bonus not available yet.")
                return
            
            # Claim daily bonus
            user.claim_daily_bonus(Config.DAILY_BONUS_AMOUNT)
            await self.bot.user_service.update_user(user)
            
            # Create transaction record
            await self.bot.transaction_service.create_transaction(
                user_id=user_id,
                amount=Config.DAILY_BONUS_AMOUNT,
                transaction_type="daily_bonus",
                description="Daily bonus claimed"
            )
            
            bonus_text = f"""
🎁 **Daily Bonus Claimed!**

You received: **₹{Config.DAILY_BONUS_AMOUNT}**
Your balance: **₹{user.balance:.2f}**

Come back tomorrow for another bonus! 🌟
            """
            
            await update.message.reply_text(
                bonus_text,
                parse_mode='Markdown'
            )
            
            log_user_action(user_id, "DAILY_BONUS_CLAIMED", f"Amount: {Config.DAILY_BONUS_AMOUNT}")
            
        except Exception as e:
            logger.error(f"Error in daily bonus command: {e}")
            await update.message.reply_text("❌ Failed to claim daily bonus.")
    
    async def referrals_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle referrals command"""
        try:
            user_id = update.effective_user.id
            
            user = await self.bot.user_service.get_user(user_id)
            if not user:
                await update.message.reply_text("❌ User not found. Please start the bot with /start")
                return
            
            # Get referral statistics
            referral_count = await self.bot.referral_service.get_successful_referral_count(user_id)
            referral_earnings = await self.bot.referral_service.get_referral_earnings(user_id)
            
            referral_text = MessageFormatter.format_referral_info(
                user.referral_code,
                referral_count,
                referral_earnings
            )
            
            await update.message.reply_text(
                referral_text,
                parse_mode='Markdown',
                reply_markup=Keyboards.referral_menu()
            )
            
            log_user_action(user_id, "REFERRALS_VIEWED")
            
        except Exception as e:
            logger.error(f"Error in referrals command: {e}")
            await update.message.reply_text("❌ Failed to get referral information.")
    
    async def withdraw_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle withdraw command"""
        try:
            user_id = update.effective_user.id
            
            user = await self.bot.user_service.get_user(user_id)
            if not user:
                await update.message.reply_text("❌ User not found. Please start the bot with /start")
                return
            
            # Check withdrawal eligibility
            eligibility = await self.bot.withdrawal_service.can_user_withdraw(user_id)
            
            if not eligibility['can_withdraw']:
                await update.message.reply_text(f"❌ {eligibility['reason']}")
                return
            
            withdraw_text = f"""
💸 **Withdrawal Options**

Your Balance: **₹{user.balance:.2f}**
Available for Withdrawal: **₹{eligibility['max_amount']:.2f}**

Choose your withdrawal option:
            """
            
            await update.message.reply_text(
                withdraw_text,
                parse_mode='Markdown',
                reply_markup=Keyboards.withdrawal_menu(user.balance)
            )
            
            log_user_action(user_id, "WITHDRAW_MENU_VIEWED")
            
        except Exception as e:
            logger.error(f"Error in withdraw command: {e}")
            await update.message.reply_text("❌ Failed to access withdrawal options.")
    
    async def stats_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle stats command"""
        try:
            user_id = update.effective_user.id
            
            # Get user statistics
            stats = await self.bot.user_service.get_user_statistics(user_id)
            
            if not stats:
                await update.message.reply_text("❌ Failed to get statistics.")
                return
            
            user_data = stats['user']
            stats_text = MessageFormatter.format_user_stats(
                user_data,
                stats['referral_count'],
                stats['transaction_count']
            )
            
            await update.message.reply_text(
                stats_text,
                parse_mode='Markdown'
            )
            
            log_user_action(user_id, "STATS_VIEWED")
            
        except Exception as e:
            logger.error(f"Error in stats command: {e}")
            await update.message.reply_text("❌ Failed to get statistics.")
    
    async def settings_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle settings command"""
        try:
            user_id = update.effective_user.id
            
            user = await self.bot.user_service.get_user(user_id)
            if not user:
                await update.message.reply_text("❌ User not found. Please start the bot with /start")
                return
            
            settings_text = f"""
⚙️ **Account Settings**

**Profile Information:**
• Name: {user.get_display_name()}
• Username: @{user.username or 'N/A'}
• User ID: `{user.user_id}`
• Language: {user.language_code or 'N/A'}

**Account Status:**
• Status: {'✅ Active' if user.is_active else '❌ Inactive'}
• Premium: {'✅ Yes' if user.is_premium else '❌ No'}
• Joined: {user.created_at.strftime('%d/%m/%Y')}

**Referral Code:** `{user.referral_code}`

Use the buttons below to manage your settings:
            """
            
            await update.message.reply_text(
                settings_text,
                parse_mode='Markdown',
                reply_markup=Keyboards.settings_menu()
            )
            
            log_user_action(user_id, "SETTINGS_VIEWED")
            
        except Exception as e:
            logger.error(f"Error in settings command: {e}")
            await update.message.reply_text("❌ Failed to access settings.")
    
    async def send_referral_qr(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Send referral QR code"""
        try:
            user_id = update.effective_user.id
            
            user = await self.bot.user_service.get_user(user_id)
            if not user:
                await update.message.reply_text("❌ User not found.")
                return
            
            referral_link = f"https://t.me/{Config.BOT_USERNAME}?start={user.referral_code}"
            
            # Generate QR code
            qr_image = QRCodeGenerator.generate_referral_qr(referral_link)
            
            await update.message.reply_photo(
                photo=qr_image,
                caption=f"📱 **Your Referral QR Code**\n\nShare this QR code with friends!\nLink: `{referral_link}`",
                parse_mode='Markdown'
            )
            
            log_user_action(user_id, "QR_CODE_GENERATED")
            
        except Exception as e:
            logger.error(f"Error generating QR code: {e}")
            await update.message.reply_text("❌ Failed to generate QR code.")
