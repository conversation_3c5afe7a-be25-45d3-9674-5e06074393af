2025-07-01 05:11:52 - src.database - ERROR - _initialize_collections:100 - Error initializing collections: Index build failed: fd679877-cef7-4fcf-bec7-5d0cda3b7ff1: Collection referral_bot.users ( ee2886e1-84e7-4639-b15f-9501d14afb5f ) :: caused by :: E11000 duplicate key error collection: referral_bot.users index: referral_code_1 dup key: { referral_code: null }, full error: {'ok': 0.0, 'errmsg': 'Index build failed: fd679877-cef7-4fcf-bec7-5d0cda3b7ff1: Collection referral_bot.users ( ee2886e1-84e7-4639-b15f-9501d14afb5f ) :: caused by :: E11000 duplicate key error collection: referral_bot.users index: referral_code_1 dup key: { referral_code: null }', 'code': 11000, 'codeName': 'DuplicateKey', 'keyPattern': {'referral_code': 1}, 'keyValue': {'referral_code': None}, '$clusterTime': {'clusterTime': Timestamp(1751326912, 28), 'signature': {'hash': b'\x89\x80\xb6A2(\x10\xde#\x90i\xdab\xd5\xd1?\xa9\x86\xb8\xb7', 'keyId': 7471222609279975432}}, 'operationTime': Timestamp(1751326912, 28)}
2025-07-01 05:11:53 - src.services.product_service - ERROR - initialize_default_products:400 - Failed to initialize default products: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:11:53 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:11:53 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:11:57 - src.bot - ERROR - start:67 - Failed to start bot: 'Updater' object has no attribute 'idle'
2025-07-01 05:11:57 - src.bot - ERROR - start:67 - Failed to start bot: 'Updater' object has no attribute 'idle'
2025-07-01 05:12:48 - src.database - ERROR - _initialize_collections:100 - Error initializing collections: Index build failed: e36f9146-93e7-4900-8434-2c3182817196: Collection referral_bot.users ( ee2886e1-84e7-4639-b15f-9501d14afb5f ) :: caused by :: E11000 duplicate key error collection: referral_bot.users index: referral_code_1 dup key: { referral_code: null }, full error: {'ok': 0.0, 'errmsg': 'Index build failed: e36f9146-93e7-4900-8434-2c3182817196: Collection referral_bot.users ( ee2886e1-84e7-4639-b15f-9501d14afb5f ) :: caused by :: E11000 duplicate key error collection: referral_bot.users index: referral_code_1 dup key: { referral_code: null }', 'code': 11000, 'codeName': 'DuplicateKey', 'keyPattern': {'referral_code': 1}, 'keyValue': {'referral_code': None}, '$clusterTime': {'clusterTime': Timestamp(1751326968, 15), 'signature': {'hash': b'\xfcrZ\xc0\x8e\x7f\xedu\xe5\xd6hl\xb2\x19D\x14\xa99\x17\xed', 'keyId': 7471222609279975432}}, 'operationTime': Timestamp(1751326968, 15)}
2025-07-01 05:12:49 - src.services.product_service - ERROR - initialize_default_products:400 - Failed to initialize default products: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:12:49 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:12:49 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:12:50 - src.bot - ERROR - start:67 - Failed to start bot: 'Updater' object has no attribute 'idle'
2025-07-01 05:12:50 - src.bot - ERROR - start:67 - Failed to start bot: 'Updater' object has no attribute 'idle'
2025-07-01 05:14:42 - src.services.product_service - ERROR - initialize_default_products:404 - Failed to initialize default products: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:14:42 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:14:42 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:14:42 - src.bot - ERROR - start:67 - Failed to start bot: Cannot close a running event loop
2025-07-01 05:14:42 - src.bot - ERROR - start:67 - Failed to start bot: Cannot close a running event loop
2025-07-01 05:24:52 - __main__ - ERROR - run:1290 - ❌ Bot error: Cannot close a running event loop
2025-07-01 06:28:56 - __main__ - ERROR - error_handler:1095 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-01 06:28:57 - __main__ - ERROR - error_handler:1095 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-01 06:28:59 - __main__ - ERROR - error_handler:1095 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-01 06:29:01 - __main__ - ERROR - error_handler:1095 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-02 09:27:46 - __main__ - ERROR - error_handler:1095 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-06 23:59:20 - __main__ - ERROR - main:1205 - ❌ Bot error: Timed out
2025-07-07 00:20:28 - __main__ - ERROR - error_handler:1581 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 00:20:32 - __main__ - ERROR - error_handler:1581 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 00:27:07 - __main__ - ERROR - balance_command:413 - Error in balance command: 'int' object has no attribute 'strftime'
