"""
User model for the Telegram Referral Bot
"""

from datetime import datetime, timezone, timedelta
from typing import Optional, Dict, Any, List
from dataclasses import dataclass, asdict
import secrets
import string
import pytz

@dataclass
class User:
    """User model representing a bot user"""
    
    user_id: int
    username: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    language_code: Optional[str] = None
    is_bot: bool = False
    is_premium: bool = False
    
    # Bot-specific fields
    balance: float = 0.0
    total_earned: float = 0.0
    referral_code: Optional[str] = None
    referred_by: Optional[int] = None
    referral_count: int = 0
    
    # Status and settings
    is_active: bool = True
    is_banned: bool = False
    ban_reason: Optional[str] = None
    
    # Subscription status
    has_joined_channels: bool = False
    joined_channels: List[str] = None
    
    # Timestamps
    created_at: datetime = None
    updated_at: datetime = None
    last_activity: datetime = None
    last_daily_bonus: Optional[datetime] = None
    
    # Statistics
    total_referrals: int = 0
    successful_referrals: int = 0
    total_withdrawals: float = 0.0
    withdrawal_count: int = 0
    
    def __post_init__(self):
        """Initialize default values after object creation"""
        if self.joined_channels is None:
            self.joined_channels = []
        
        if self.created_at is None:
            self.created_at = datetime.now(timezone.utc)
        
        if self.updated_at is None:
            self.updated_at = datetime.now(timezone.utc)
        
        if self.last_activity is None:
            self.last_activity = datetime.now(timezone.utc)
        
        if self.referral_code is None:
            self.referral_code = self.generate_referral_code()
    
    @staticmethod
    def generate_referral_code(length: int = 8) -> str:
        """Generate a unique referral code"""
        characters = string.ascii_uppercase + string.digits
        return ''.join(secrets.choice(characters) for _ in range(length))
    
    def update_activity(self):
        """Update last activity timestamp"""
        self.last_activity = datetime.now(timezone.utc)
        self.updated_at = datetime.now(timezone.utc)
    
    def add_balance(self, amount: float, reason: str = ""):
        """Add amount to user balance"""
        self.balance += amount
        self.total_earned += amount
        self.update_activity()
    
    def deduct_balance(self, amount: float) -> bool:
        """Deduct amount from user balance if sufficient funds"""
        if self.balance >= amount:
            self.balance -= amount
            self.update_activity()
            return True
        return False
    
    def can_claim_daily_bonus(self) -> bool:
        """Check if user can claim daily bonus (resets daily at 12:00 AM IST)"""
        if not self.last_daily_bonus:
            return True

        # Get current time in IST (GMT+5:30)
        ist_tz = pytz.timezone('Asia/Kolkata')
        now_ist = datetime.now(ist_tz)

        # Convert last claim time to IST
        last_claim_utc = self.last_daily_bonus.replace(tzinfo=timezone.utc)
        last_claim_ist = last_claim_utc.astimezone(ist_tz)

        # Check if it's a different day in IST
        return now_ist.date() > last_claim_ist.date()
    
    def get_next_daily_bonus_time_ist(self) -> Optional[datetime]:
        """Get the next daily bonus claim time in IST"""
        if not self.last_daily_bonus:
            return None

        # Get IST timezone
        ist_tz = pytz.timezone('Asia/Kolkata')

        # Convert last claim time to IST
        last_claim_utc = self.last_daily_bonus.replace(tzinfo=timezone.utc)
        last_claim_ist = last_claim_utc.astimezone(ist_tz)

        # Next claim is at 12:00 AM IST the next day
        next_claim_date = last_claim_ist.date() + timedelta(days=1)
        next_claim_ist = ist_tz.localize(datetime.combine(next_claim_date, datetime.min.time()))

        return next_claim_ist

    def claim_daily_bonus(self, amount: float):
        """Claim daily bonus"""
        self.add_balance(amount, "Daily Bonus")
        self.last_daily_bonus = datetime.now(timezone.utc)
    
    def add_referral(self):
        """Increment referral count"""
        self.referral_count += 1
        self.successful_referrals += 1
        self.update_activity()
    
    def ban_user(self, reason: str = ""):
        """Ban the user"""
        self.is_banned = True
        self.is_active = False
        self.ban_reason = reason
        self.update_activity()
    
    def unban_user(self):
        """Unban the user"""
        self.is_banned = False
        self.is_active = True
        self.ban_reason = None
        self.update_activity()
    
    def join_channel(self, channel_id: str):
        """Mark channel as joined"""
        if channel_id not in self.joined_channels:
            self.joined_channels.append(channel_id)
            self.update_activity()
    
    def leave_channel(self, channel_id: str):
        """Mark channel as left"""
        if channel_id in self.joined_channels:
            self.joined_channels.remove(channel_id)
            self.update_activity()
    
    def get_display_name(self) -> str:
        """Get user's display name"""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        elif self.username:
            return f"@{self.username}"
        else:
            return f"User {self.user_id}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert user object to dictionary"""
        data = asdict(self)
        
        # Convert datetime objects to ISO format
        for key, value in data.items():
            if isinstance(value, datetime):
                data[key] = value.isoformat()
        
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'User':
        """Create user object from dictionary"""
        # Create a copy of data to avoid modifying the original
        user_data = data.copy()

        # Remove MongoDB's _id field if present
        user_data.pop('_id', None)

        # Convert ISO format strings back to datetime objects
        datetime_fields = ['created_at', 'updated_at', 'last_activity', 'last_daily_bonus']

        for field in datetime_fields:
            if field in user_data and user_data[field]:
                if isinstance(user_data[field], str):
                    user_data[field] = datetime.fromisoformat(user_data[field].replace('Z', '+00:00'))

        return cls(**user_data)
    
    def __str__(self) -> str:
        """String representation of user"""
        return f"User(id={self.user_id}, name={self.get_display_name()}, balance={self.balance})"
